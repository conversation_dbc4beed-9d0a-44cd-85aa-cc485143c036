from django.urls import include, path
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.reverse import reverse
from rest_framework.routers import DefaultRouter

from store import views

# DRF Router
router = DefaultRouter()
router.register(r"organizations", views.OrganizationViewSet, basename="organization")
router.register(r"hero-slides", views.HeroSlideViewSet, basename="heroslide")
router.register(
    r"about-content", views.AboutPageContentViewSet, basename="aboutpagecontent"
)
router.register(r"testimonials", views.TestimonialViewSet, basename="testimonial")


@api_view(["GET"])
def store_root(request, format=None):
    return Response(
        {
            "organizations": reverse(
                "organization-list", request=request, format=format
            ),
            "hero-slides": reverse("heroslide-list", request=request, format=format),
            "about-content": reverse(
                "aboutpagecontent-list", request=request, format=format
            ),
            "testimonials": reverse("testimonial-list", request=request, format=format),
        }
    )


urlpatterns = [
    path("", store_root, name="store-api-root"),
    path("", include(router.urls)),
    # Organization custom actions
    path(
        "organizations/active/",
        views.OrganizationViewSet.as_view({"get": "active"}),
        name="organization-active",
    ),
    # About content custom actions
    path(
        "about-content/active/",
        views.AboutPageContentViewSet.as_view({"get": "active"}),
        name="aboutcontent-active",
    ),
    path(
        "about-content/by-type/",
        views.AboutPageContentViewSet.as_view({"get": "by_type"}),
        name="aboutcontent-by-type",
    ),
    path(
        "about-content/<int:pk>/toggle-active/",
        views.AboutPageContentViewSet.as_view({"post": "toggle_active"}),
        name="aboutcontent-toggle-active",
    ),
    # Testimonial custom actions
    path(
        "testimonials/featured/",
        views.TestimonialViewSet.as_view({"get": "featured"}),
        name="testimonial-featured",
    ),
    path(
        "testimonials/by-rating/",
        views.TestimonialViewSet.as_view({"get": "by_rating"}),
        name="testimonial-by-rating",
    ),
    path(
        "testimonials/my-testimonials/",
        views.TestimonialViewSet.as_view({"get": "my_testimonials"}),
        name="testimonial-my-testimonials",
    ),
    path(
        "testimonials/<int:pk>/approve/",
        views.TestimonialViewSet.as_view({"post": "approve"}),
        name="testimonial-approve",
    ),
    path(
        "testimonials/<int:pk>/feature/",
        views.TestimonialViewSet.as_view({"post": "feature"}),
        name="testimonial-feature",
    ),
    path(
        "testimonials/bulk-approve/",
        views.TestimonialViewSet.as_view({"post": "bulk_approve"}),
        name="testimonial-bulk-approve",
    ),
]
