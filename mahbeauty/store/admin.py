from django.contrib import admin
from django.contrib.admin import SimpleListFilter
from django.db.models import Count
from django.urls import reverse

from store.models import (
    AboutPageContent,
    HeroSlide,
    Organization,
    HeroSlideImage,
    Testimonial,
)


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "logo_preview",
        "location",
        "phone",
        "email",
        "social_links_count",
    )
    search_fields = ("name", "location", "phone", "email")
    readonly_fields = ("logo_preview_large", "social_media_summary")
    list_per_page = 10

    fieldsets = (
        (
            "Basic Information",
            {"fields": ("name", "logo", "logo_preview_large", "location")},
        ),
        ("Contact Information", {"fields": ("phone", "email", "mapping_url")}),
        (
            "Social Media",
            {
                "fields": (
                    "instagram",
                    "facebook",
                    "youtube",
                    "tiktok",
                    "social_media_summary",
                ),
                "classes": ("collapse",),
            },
        ),
    )

    def logo_preview(self, obj):
        if obj.logo:
            return f"Logo: {obj.logo.name}"
        return "No Logo"

    logo_preview.short_description = "Logo"

    def logo_preview_large(self, obj):
        if obj.logo:
            return f"Logo: {obj.logo.name}"
        return "No Logo"

    logo_preview_large.short_description = "Organization Logo"

    def social_links_count(self, obj):
        links = [obj.instagram, obj.facebook, obj.youtube, obj.tiktok]
        active_links = sum(1 for link in links if link)
        if active_links > 0:
            return f"{active_links}/4 platforms"
        return "No social links"

    social_links_count.short_description = "Social Media"

    def social_media_summary(self, obj):
        social_platforms = [
            ("Instagram", obj.instagram),
            ("Facebook", obj.facebook),
            ("YouTube", obj.youtube),
            ("TikTok", obj.tiktok),
        ]

        summary = []
        for platform, url in social_platforms:
            if url:
                summary.append(f"{platform}: {url}")
            else:
                summary.append(f"{platform}: Not configured")
        
        return "\n".join(summary)

    social_media_summary.short_description = "Social Media Links"


class HeroSlideImageInline(admin.TabularInline):
    model = HeroSlideImage
    extra = 1
    fields = ("image", "order")
    ordering = ("order",)


@admin.register(HeroSlide)
class HeroSlideAdmin(admin.ModelAdmin):
    list_display = (
        "title",
        "description_preview",
        "stats_display",
        "image_count",
        "created_at",
        "updated_at",
    )
    search_fields = ("title", "description")
    readonly_fields = ("created_at", "updated_at", "slide_summary")
    ordering = ("-created_at",)
    list_per_page = 20

    fieldsets = (
        ("Slide Content", {"fields": ("title", "description")}),
        (
            "Statistics",
            {
                "fields": ("client", "service", "experience"),
                "description": "Optional statistics to display on the slide",
            },
        ),
        ("Slide Summary", {"fields": ("slide_summary",), "classes": ("collapse",)}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    inlines = [HeroSlideImageInline]

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(_image_count=Count("images"))
        return queryset

    def description_preview(self, obj):
        if obj.description:
            preview = (
                obj.description[:50] + "..."
                if len(obj.description) > 50
                else obj.description
            )
            return preview
        return "No description"

    description_preview.short_description = "Description"

    def stats_display(self, obj):
        stats = []
        if obj.client:
            stats.append(f"{obj.client} clients")
        if obj.service:
            stats.append(f"{obj.service} services")
        if obj.experience:
            stats.append(f"{obj.experience}+ years")

        if stats:
            return " | ".join(stats)
        return "No stats"

    stats_display.short_description = "Statistics"

    def image_count(self, obj):
        count = obj._image_count
        if count > 0:
            return f"{count} images"
        return "No images"

    image_count.short_description = "Images"
    image_count.admin_order_field = "_image_count"

    def slide_summary(self, obj):
        images = obj.images.all()
        summary = f"""
Slide Overview:
Title: {obj.title or "No title"}
Description: {obj.description[:100] + "..." if obj.description and len(obj.description) > 100 else obj.description or "No description"}
Images: {images.count()} attached
Statistics: {f"Clients: {obj.client}, " if obj.client else ""}{f"Services: {obj.service}, " if obj.service else ""}{f"Experience: {obj.experience} years" if obj.experience else ""}
        """
        return summary.strip()

    slide_summary.short_description = "Slide Summary"


class ContentTypeFilter(SimpleListFilter):
    title = "Content Type"
    parameter_name = "content_type"

    def lookups(self, request, model_admin):
        return AboutPageContent.CONTENT_TYPE_CHOICES

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(content_type=self.value())


@admin.register(AboutPageContent)
class AboutPageContentAdmin(admin.ModelAdmin):
    list_display = (
        "content_type_display",
        "title",
        "subtitle_preview",
        "image_preview",
        "status_display",
        "is_active",
        "created_at",
        "updated_at",
    )
    list_filter = (ContentTypeFilter, "is_active", "created_at")
    search_fields = ("title", "subtitle", "content")
    list_editable = ("is_active",)
    readonly_fields = (
        "created_at",
        "updated_at",
        "image_preview_large",
        "content_preview",
    )
    ordering = ("content_type",)
    list_per_page = 20

    fieldsets = (
        ("Content Information", {"fields": ("content_type", "title", "subtitle")}),
        (
            "Content Body",
            {"fields": ("content", "content_preview"), "classes": ("wide",)},
        ),
        (
            "Media",
            {"fields": ("image", "image_preview_large"), "classes": ("collapse",)},
        ),
        (
            "Status & Timestamps",
            {
                "fields": ("is_active", "created_at", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )

    actions = ["activate_content", "deactivate_content"]

    def content_type_display(self, obj):
        return obj.get_content_type_display()

    content_type_display.short_description = "Type"
    content_type_display.admin_order_field = "content_type"

    def subtitle_preview(self, obj):
        if obj.subtitle:
            preview = (
                obj.subtitle[:30] + "..." if len(obj.subtitle) > 30 else obj.subtitle
            )
            return preview
        return "No subtitle"

    subtitle_preview.short_description = "Subtitle"

    def image_preview(self, obj):
        if obj.image:
            return f"Image: {obj.image.name}"
        return "No image"

    image_preview.short_description = "Image"

    def image_preview_large(self, obj):
        if obj.image:
            return f"Image: {obj.image.name}"
        return "No Image"

    image_preview_large.short_description = "Content Image"

    def status_display(self, obj):
        if obj.is_active:
            return "Active"
        return "Inactive"

    status_display.short_description = "Status"
    status_display.admin_order_field = "is_active"

    def content_preview(self, obj):
        if obj.content:
            preview = (
                obj.content[:300] + "..." if len(obj.content) > 300 else obj.content
            )
            return preview
        return "No content"

    content_preview.short_description = "Content Preview"

    def activate_content(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f"{updated} content items activated.")

    activate_content.short_description = "Activate selected content"

    def deactivate_content(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f"{updated} content items deactivated.")

    deactivate_content.short_description = "Deactivate selected content"


class TestimonialStatusFilter(SimpleListFilter):
    title = "Featured Status"
    parameter_name = "featured_status"

    def lookups(self, request, model_admin):
        return (
            ("featured", "Featured"),
            ("regular", "Regular"),
        )

    def queryset(self, request, queryset):
        if self.value() == "featured":
            return queryset.filter(is_featured=True)
        elif self.value() == "regular":
            return queryset.filter(is_featured=False)


class RatingFilter(SimpleListFilter):
    title = "Rating"
    parameter_name = "rating"

    def lookups(self, request, model_admin):
        return [(i, f"{i} Star{'s' if i > 1 else ''}") for i in range(1, 6)]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(rating=int(self.value()))


@admin.register(Testimonial)
class TestimonialAdmin(admin.ModelAdmin):
    list_display = (
        "user_display",
        "service_name",
        "rating_display",
        "featured_status",
        "is_featured",
        "created_at",
    )
    list_filter = (TestimonialStatusFilter, RatingFilter, "is_featured", "created_at")
    search_fields = ("user__email", "user__first_name", "user__last_name", "comment", "service_name")
    list_editable = ("is_featured",)
    readonly_fields = ("created_at", "updated_at", "testimonial_summary")
    ordering = ("-created_at",)
    list_per_page = 25
    date_hierarchy = "created_at"

    fieldsets = (
        ("Testimonial Information", {"fields": ("user", "comment", "service_name", "rating")}),
        ("Status", {"fields": ("is_featured",)}),
        ("Summary", {"fields": ("testimonial_summary",), "classes": ("collapse",)}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    actions = ["feature_testimonials", "unfeature_testimonials"]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("user")

    def user_display(self, obj):
        display_name = (
            f"{obj.user.first_name} {obj.user.last_name}".strip() or obj.user.email
        )
        url = reverse("admin:account_useraccount_change", args=[obj.user.pk])
        return f"{display_name} (ID: {obj.user.pk})"

    user_display.short_description = "User"
    user_display.admin_order_field = "user__email"

    def rating_display(self, obj):
        stars = "★" * obj.rating + "☆" * (5 - obj.rating)
        return f"{obj.rating}/5 - {stars}"

    rating_display.short_description = "Rating"
    rating_display.admin_order_field = "rating"

    def featured_status(self, obj):
        if obj.is_featured:
            return "Featured"
        return "Regular"

    featured_status.short_description = "Featured"
    featured_status.admin_order_field = "is_featured"

    def testimonial_summary(self, obj):
        summary = f"""
Testimonial Overview:
User: {obj.user.first_name} {obj.user.last_name} ({obj.user.email})
Service: {obj.service_name}
Rating: {"★" * obj.rating}{"☆" * (5 - obj.rating)} ({obj.rating}/5)
Status: {"Featured" if obj.is_featured else "Regular"}
Comment: {obj.comment[:100]}{"..." if len(obj.comment) > 100 else ""}
Created: {obj.created_at.strftime("%B %d, %Y at %I:%M %p")}
        """
        return summary.strip()

    testimonial_summary.short_description = "Testimonial Summary"

    def feature_testimonials(self, request, queryset):
        updated = queryset.update(is_featured=True)
        self.message_user(request, f"{updated} testimonials featured.")

    feature_testimonials.short_description = "Feature selected testimonials"

    def unfeature_testimonials(self, request, queryset):
        updated = queryset.update(is_featured=False)
        self.message_user(request, f"{updated} testimonials unfeatured.")

    unfeature_testimonials.short_description = "Unfeature selected testimonials"



