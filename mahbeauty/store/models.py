from django.core.validators import RegexValidator
from django.db import models

from account.models import UserAccount

# Create your models here.


class Organization(models.Model):
    name = models.CharField(max_length=255, unique=True)
    logo = models.ImageField(upload_to="organization_logos/", blank=True, null=True)
    location = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=10, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    mapping_url = models.URLField(blank=True, null=True)
    instagram = models.URLField(blank=True, null=True)
    facebook = models.URLField(blank=True, null=True)
    youtube = models.URLField(blank=True, null=True)
    tiktok = models.URLField(blank=True, null=True)

    class Meta:
        db_table = "organization"
        verbose_name = "Store Information"
        verbose_name_plural = "Store Information"

    def __str__(self):
        return self.name


class HeroSlide(models.Model):
    title = models.Char<PERSON><PERSON>(max_length=250, blank=True)
    description = models.TextField(blank=True)
    client = models.PositiveIntegerField(blank=True, null=True)
    service = models.PositiveIntegerField(blank=True, null=True)
    experience = models.PositiveIntegerField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title
    
    class Meta:
        db_table = "hero_slides"
        verbose_name = "Homepage Banner"
        verbose_name_plural = "Homepage Banners"


class HeroSlideImage(models.Model):
    slide = models.ForeignKey(
        HeroSlide, on_delete=models.CASCADE, related_name="images"
    )
    image = models.ImageField(upload_to="hero_slides/")
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ["order"]
        db_table = "hero_slide_images"
        verbose_name = "Banner Image"
        verbose_name_plural = "Banner Images"

    def __str__(self):
        return f"Image for slide {self.slide.id}"


class AboutPageContent(models.Model):
    CONTENT_TYPE_CHOICES = [
        ("story", "Our Story"),
        ("mission", "Our Mission"),
        ("vision", "Our Vision"),
        ("values", "Core Values"),
        ("header", "Page Header"),
    ]

    id = models.AutoField(primary_key=True)
    content_type = models.CharField(
        max_length=20, choices=CONTENT_TYPE_CHOICES, unique=True
    )
    title = models.CharField(max_length=200)
    content = models.TextField()
    subtitle = models.CharField(max_length=300, blank=True)
    image = models.ImageField(upload_to="about_content/", blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "about_page_content"
        ordering = ["content_type"]
        verbose_name = "About Us"
        verbose_name_plural = "About Us"

    def __str__(self):
        return f"{self.get_content_type_display()} - {self.title}"


class Testimonial(models.Model):
    RATING_CHOICES = [(i, i) for i in range(1, 6)]

    id = models.AutoField(primary_key=True)
    user = models.ForeignKey(
        UserAccount, on_delete=models.CASCADE, related_name="testimonials"
    )
    comment = models.TextField(help_text="Customer review/testimonial")
    service_name = models.CharField(max_length=200, help_text="Service reviewed")
    is_featured = models.BooleanField(default=False)
    rating = models.PositiveIntegerField(choices=RATING_CHOICES, default=5)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "testimonials"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["rating"]),
            models.Index(fields=["is_featured"]),
        ]
        verbose_name = "Customer Review & Rating"
        verbose_name_plural = "Customer Reviews & Ratings"

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.rating} stars"
