# Store App API Documentation

This document describes the REST API endpoints for the store app, including serializers and viewsets for managing organization information, hero slides, about page content, and testimonials.

## 📋 Overview

The store app provides the following API endpoints:
- **Organizations**: Company/business information
- **Hero Slides**: Homepage carousel slides with images
- **About Page Content**: Different sections of the about page
- **Testimonials**: Customer reviews and ratings

## 🔗 Base URL

All endpoints are prefixed with `/store/api/`

## 📊 Models & Endpoints

### 1. Organization API

**Base URL**: `/store/api/organizations/`

#### Endpoints:
- `GET /store/api/organizations/` - List all organizations
- `POST /store/api/organizations/` - Create new organization (Admin only)
- `GET /store/api/organizations/{id}/` - Get specific organization
- `PUT/PATCH /store/api/organizations/{id}/` - Update organization (Admin only)
- `DELETE /store/api/organizations/{id}/` - Delete organization (Admin only)
- `GET /store/api/organizations/active/` - Get main organization info

#### Fields:
```json
{
    "id": 1,
    "name": "Beauty Salon",
    "logo": "http://example.com/media/organization_logos/logo.jpg",
    "location": "New York, NY",
    "phone": "1234567890",
    "email": "<EMAIL>",
    "mapping_url": "https://maps.google.com/...",
    "instagram": "https://instagram.com/beautysalon",
    "facebook": "https://facebook.com/beautysalon",
    "youtube": "https://youtube.com/beautysalon",
    "tiktok": "https://tiktok.com/@beautysalon"
}
```

#### Permissions:
- **Read**: Anyone
- **Create/Update/Delete**: Admin users only

### 2. Hero Slides API

**Base URL**: `/store/api/hero-slides/`

#### Endpoints:
- `GET /store/api/hero-slides/` - List all hero slides
- `POST /store/api/hero-slides/` - Create new slide (Admin only)
- `GET /store/api/hero-slides/{id}/` - Get specific slide
- `PUT/PATCH /store/api/hero-slides/{id}/` - Update slide (Admin only)
- `DELETE /store/api/hero-slides/{id}/` - Delete slide (Admin only)
- `POST /store/api/hero-slides/{id}/add-image/` - Add image to slide (Admin only)
- `DELETE /store/api/hero-slides/{id}/remove-image/` - Remove image from slide (Admin only)

#### Fields:
```json
{
    "id": 1,
    "title": "Welcome to Our Beauty Salon",
    "description": "Experience luxury beauty treatments",
    "client": 500,
    "service": 25,
    "experience": 10,
    "images": [
        {
            "id": 1,
            "image": "http://example.com/media/hero_slides/slide1.jpg",
            "order": 0
        }
    ],
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
}
```

#### Creating Slides with Images:
```json
{
    "title": "New Slide",
    "description": "Slide description",
    "client": 100,
    "service": 5,
    "experience": 2,
    "images_data": [
        // Array of image files
    ]
}
```

#### Permissions:
- **Read**: Anyone
- **Create/Update/Delete**: Admin users only

### 3. About Page Content API

**Base URL**: `/store/api/about-content/`

#### Endpoints:
- `GET /store/api/about-content/` - List all content sections
- `POST /store/api/about-content/` - Create new content (Admin only)
- `GET /store/api/about-content/{id}/` - Get specific content
- `PUT/PATCH /store/api/about-content/{id}/` - Update content (Admin only)
- `DELETE /store/api/about-content/{id}/` - Delete content (Admin only)
- `GET /store/api/about-content/active/` - Get all active content
- `GET /store/api/about-content/by-type/?type=story` - Get content by type
- `POST /store/api/about-content/{id}/toggle-active/` - Toggle active status (Admin only)

#### Content Types:
- `story` - Our Story
- `mission` - Our Mission
- `vision` - Our Vision
- `values` - Core Values
- `header` - Page Header

#### Fields:
```json
{
    "id": 1,
    "content_type": "story",
    "content_type_display": "Our Story",
    "title": "Our Journey",
    "content": "We started our beauty salon...",
    "subtitle": "A story of passion and dedication",
    "image": "http://example.com/media/about_content/story.jpg",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
}
```

#### Filtering:
- `?content_type=story` - Filter by content type
- `?is_active=true` - Filter by active status
- `?search=beauty` - Search in title, content, subtitle

#### Permissions:
- **Read**: Anyone
- **Create/Update/Delete**: Admin users only

### 4. Testimonials API

**Base URL**: `/store/api/testimonials/`

#### Endpoints:
- `GET /store/api/testimonials/` - List testimonials
- `POST /store/api/testimonials/` - Create testimonial (Authenticated users)
- `GET /store/api/testimonials/{id}/` - Get specific testimonial
- `PUT/PATCH /store/api/testimonials/{id}/` - Update own testimonial
- `DELETE /store/api/testimonials/{id}/` - Delete own testimonial
- `GET /store/api/testimonials/featured/` - Get featured testimonials
- `GET /store/api/testimonials/by-rating/?rating=5` - Get testimonials by rating
- `GET /store/api/testimonials/my-testimonials/` - Get current user's testimonials
- `POST /store/api/testimonials/{id}/approve/` - Approve testimonial (Admin only)
- `POST /store/api/testimonials/{id}/feature/` - Toggle featured status (Admin only)
- `POST /store/api/testimonials/bulk-approve/` - Bulk approve testimonials (Admin only)

#### Fields:
```json
{
    "id": 1,
    "user": {
        "id": 1,
        "first_name": "John",
        "last_name": "Doe",
        "full_name": "John Doe",
        "email": "<EMAIL>"
    },
    "is_featured": false,
    "rating": 5,
    "rating_display": "5",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
}
```

#### Creating Testimonials:
```json
{
    "rating": 5
}
```

#### Bulk Approve:
```json
{
    "testimonial_ids": [1, 2, 3, 4, 5]
}
```

#### Filtering:
- `?rating=5` - Filter by rating
- `?is_featured=true` - Filter by featured status
- `?search=john` - Search by user name or email

#### Permissions:
- **Read**: Anyone
- **Create**: Authenticated users
- **Update/Delete**: Own testimonials only (or admin)
- **Feature**: Admin users only

## 🔐 Authentication

The API uses JWT authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

## 📝 Response Format

All responses follow this format:

**Success Response:**
```json
{
    "data": { ... },
    "status": "success"
}
```

**Error Response:**
```json
{
    "error": "Error message",
    "status": "error"
}
```

## 🔍 Filtering & Search

Most endpoints support:
- **Search**: `?search=keyword`
- **Ordering**: `?ordering=field_name` or `?ordering=-field_name`
- **Filtering**: `?field_name=value`

## 📄 Pagination

List endpoints support pagination:
- `?page=1` - Page number
- `?page_size=10` - Items per page

## ⚠️ Notes

1. **Image uploads**: Use multipart/form-data for image uploads
2. **Content types**: Each about page content type must be unique
3. **Phone validation**: Phone numbers must be exactly 10 digits
4. **Rating validation**: Ratings must be between 1 and 5
5. **Testimonial featuring**: Only admin users can feature testimonials
