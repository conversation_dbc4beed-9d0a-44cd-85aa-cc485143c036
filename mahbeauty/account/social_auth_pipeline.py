"""
Custom social authentication pipeline for account linking
"""

from social_core.exceptions import AuthAlreadyAssociated
from social_django.models import UserSocialAuth
from account.models import UserAccount
from account.utils import get_social_profile_image_url, save_profile_image_from_url
import logging

logger = logging.getLogger(__name__)


def associate_by_email(backend, details, user=None, *args, **kwargs):
    """
    Associate current social login with existing user account if email matches.
    
    This pipeline step runs before user creation and links Google/Facebook
    accounts to existing email/password accounts.
    """
    
    if user:
        # User is already authenticated, continue
        return
    
    email = details.get('email')
    if not email:
        # No email provided by social provider, continue
        return
    
    try:
        # Try to find existing user with this email
        existing_user = UserAccount.objects.get(email=email)
        
        # Check if this social account is already associated with any user
        try:
            social_user = UserSocialAuth.objects.get(
                provider=backend.name,
                uid=kwargs['uid']
            )

            if social_user.user != existing_user:
                # Social account is associated with different user
                raise AuthAlreadyAssociated(backend, 'Social account already in use')
            else:
                # Social account is already linked to this user, continue
                return {
                    'user': existing_user,
                    'is_new': False
                }

        except UserSocialAuth.DoesNotExist:
            # Social account not associated yet, we can link it to existing user
            pass
        
        # Link the social account to existing user
        return {
            'user': existing_user,
            'is_new': False
        }
        
    except UserAccount.DoesNotExist:
        # No existing user with this email, continue with normal flow
        return


def create_user_with_social_data(strategy, details, backend, user=None, *args, **kwargs):
    """
    Create user with social data if no existing user found.
    
    This runs after associate_by_email, so only creates new users
    when no existing account was found.
    """
    
    if user:
        # User already exists (from associate_by_email or elsewhere)
        return {'is_new': False}
    
    # Extract user data from social provider
    email = details.get('email')
    first_name = details.get('first_name', '')
    last_name = details.get('last_name', '')
    
    if not email:
        # Cannot create user without email
        return
    
    # Create new user account
    new_user = UserAccount.objects.create_user(
        email=email,
        first_name=first_name,
        last_name=last_name,
    )
    
    return {
        'user': new_user,
        'is_new': True
    }


def save_profile_image_from_social(strategy, details, backend, user=None, response=None, *args, **kwargs):
    """
    Save profile image from social provider during authentication.

    This pipeline step downloads and saves the user's profile image from
    the social authentication provider if available.
    """

    if not user:
        return

    # Only process for new users or users without profile images
    is_new = kwargs.get('is_new', False)
    if not is_new and user.has_profile_image():
        return

    try:
        # Extract profile image URL from response or details
        image_url = None

        if backend.name == 'google-oauth2':
            # Google provides picture URL directly in response
            image_url = response.get('picture') if response else details.get('picture')

        elif backend.name == 'facebook':
            # Facebook picture URL might be in response or details
            if response:
                picture_data = response.get('picture', {})
                if isinstance(picture_data, dict):
                    data = picture_data.get('data', {})
                    if isinstance(data, dict):
                        image_url = data.get('url')

        if image_url:
            # Try to save the profile image
            success = save_profile_image_from_url(user, image_url)
            if success:
                logger.info(f"Successfully saved profile image for user {user.id} from {backend.name}")
            else:
                logger.debug(f"Could not save profile image for user {user.id} from {backend.name}")

    except Exception as e:
        logger.error(f"Error saving profile image for user {user.id} from {backend.name}: {e}")

    return {'user': user}


def update_user_social_data(strategy, details, backend, user=None, response=None, *args, **kwargs):
    """
    Update user profile with latest social data including profile image.

    This keeps the user's profile updated with latest info from social provider.
    """

    if not user:
        return

    # Get latest data from social provider
    first_name = details.get('first_name', '')
    last_name = details.get('last_name', '')

    # Update user profile if social data is more complete
    updated = False

    if first_name and not user.first_name:
        user.first_name = first_name
        updated = True

    if last_name and not user.last_name:
        user.last_name = last_name
        updated = True

    if updated:
        user.save()

    # Handle profile image from social provider
    try:
        # Get the social auth record to access extra_data
        social_user = user.social_auth.filter(provider=backend.name).first()
        if social_user and social_user.extra_data:
            # Extract profile image URL
            image_url = get_social_profile_image_url(backend.name, social_user.extra_data)

            if image_url:
                # Try to save the profile image (only if user doesn't have one)
                success = save_profile_image_from_url(user, image_url)
                if success:
                    logger.info(f"Successfully saved profile image for user {user.id} from {backend.name}")
                else:
                    logger.debug(f"Profile image not saved for user {user.id} (may already have one)")

    except Exception as e:
        logger.error(f"Error handling profile image for user {user.id}: {e}")

    return {'user': user}


# Pipeline configuration for settings.py
SOCIAL_AUTH_PIPELINE = (
    # Get the information we can about the user and return it in a simple
    # format to create the user instance later. In some cases the details are
    # already part of the auth response from the provider, but sometimes this
    # could hit a provider API.
    'social_core.pipeline.social_auth.social_details',

    # Get the social uid from whichever service we're authing thru. The uid is
    # the unique identifier of the given user in the provider.
    'social_core.pipeline.social_auth.social_uid',

    # Verifies that the current auth process is valid within the current
    # project, this is where emails and domains whitelists are applied (if
    # defined).
    'social_core.pipeline.social_auth.auth_allowed',

    # Checks if the current social-account is already associated in the site.
    'social_core.pipeline.social_auth.social_user',

    # Make up a username for this person, appends a random string at the end if
    # there's any collision.
    'social_core.pipeline.user.get_username',

    # CUSTOM: Associate by email before creating new user
    'account.social_auth_pipeline.associate_by_email',

    # Create a user account if we haven't found one yet.
    'account.social_auth_pipeline.create_user_with_social_data',

    # Create the record that associates the social account with the user.
    'social_core.pipeline.social_auth.associate_user',

    # Populate the extra_data field in the social record with the values
    # specified by settings (and the default ones like access_token, etc).
    'social_core.pipeline.social_auth.load_extra_data',

    # CUSTOM: Update user profile with social data
    'account.social_auth_pipeline.update_user_social_data',
)
