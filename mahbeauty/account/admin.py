from django.contrib import admin
from django.contrib.admin import Simple<PERSON>ist<PERSON>ilter
from django.contrib.auth.admin import UserAdmin
from django.db.models import Avg, Count, Sum
from django.urls import reverse
from django.utils import timezone

from account.models import UserAccount


# Proxy models for different user types to register separately
class StaffUserAccount(UserAccount):
    class Meta:
        proxy = True
        verbose_name = "Staff Account"
        verbose_name_plural = "Staff Accounts"


class SuperuserAccount(UserAccount):
    class Meta:
        proxy = True
        verbose_name = "Admin Account"
        verbose_name_plural = "Admin Accounts"


class UserTypeFilter(SimpleListFilter):
    title = "User Type"
    parameter_name = "user_type"

    def lookups(self, request, model_admin):
        return (
            ("google", "Google Users"),
            ("regular", "Regular Users"),
            ("staff", "Staff Users"),
            ("superuser", "Superusers"),
            ("active_customers", "Active Customers"),
            ("inactive", "Inactive Users"),
        )

    def queryset(self, request, queryset):
        if self.value() == "google":
            return queryset.filter(google_id__isnull=False)
        elif self.value() == "regular":
            return queryset.filter(
                google_id__isnull=True, is_staff=False, is_superuser=False
            )
        elif self.value() == "staff":
            return queryset.filter(is_staff=True, is_superuser=False)
        elif self.value() == "superuser":
            return queryset.filter(is_superuser=True)
        elif self.value() == "active_customers":
            return queryset.filter(is_active=True, order__isnull=False).distinct()
        elif self.value() == "inactive":
            return queryset.filter(is_active=False)


class ActivityFilter(SimpleListFilter):
    title = "Activity Level"
    parameter_name = "activity_level"

    def lookups(self, request, model_admin):
        return (
            ("high", "High Activity (5+ orders)"),
            ("medium", "Medium Activity (2-4 orders)"),
            ("low", "Low Activity (1 order)"),
            ("no_orders", "No Orders"),
        )

    def queryset(self, request, queryset):
        if self.value() == "high":
            return queryset.annotate(order_count=Count("order")).filter(
                order_count__gte=5
            )
        elif self.value() == "medium":
            return queryset.annotate(order_count=Count("order")).filter(
                order_count__range=(2, 4)
            )
        elif self.value() == "low":
            return queryset.annotate(order_count=Count("order")).filter(order_count=1)
        elif self.value() == "no_orders":
            return queryset.annotate(order_count=Count("order")).filter(order_count=0)


class BaseUserAccountAdmin(UserAdmin):
    list_display = (
        "email",
        "full_name",
        "user_type",
        "activity_level",
        "order_count",
        "total_spent",
        "is_active",
        "last_login",
    )
    list_filter = (
        UserTypeFilter,
        ActivityFilter,
        "is_active",
        "is_staff",
        "is_superuser",
        "last_login",
    )
    search_fields = ("email", "first_name", "last_name", "google_id")
    list_editable = ("is_active",)
    readonly_fields = (
        "last_login",
        "user_summary",
        "order_history",
        "cart_summary",
        "review_summary",
        "testimonial_summary",
    )
    ordering = ("-last_login",)
    list_per_page = 10

    fieldsets = (
        ("Account Information", {"fields": ("email", "password")}),
        ("Personal Information", {"fields": ("first_name", "last_name", "google_id", "profile_image")}),
        (
            "Permissions",
            {
                "fields": (
                    "is_active",
                    "is_staff",
                    "is_superuser",
                    "groups",
                    "user_permissions",
                ),
                "classes": ("collapse",),
            },
        ),
        ("Important Dates", {"fields": ("last_login",), "classes": ("collapse",)}),
        (
            "User Activity Summary",
            {"fields": ("user_summary",), "classes": ("collapse",)},
        ),
        ("Order History", {"fields": ("order_history",), "classes": ("collapse",)}),
        (
            "Shopping Activity",
            {
                "fields": ("cart_summary", "review_summary", "testimonial_summary"),
                "classes": ("collapse",),
            },
        ),
    )

    add_fieldsets = (
        (
            "Create New User",
            {
                "classes": ("wide",),
                "fields": (
                    "email",
                    "first_name",
                    "last_name",
                    "profile_image",
                    "password1",
                    "password2",
                    "is_active",
                ),
            },
        ),
    )

    actions = ["activate_users", "deactivate_users", "make_staff", "remove_staff"]

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        # Only annotate customer-related metrics for non-staff, non-superuser accounts
        if not request.user.is_staff:
            # For regular users viewing the admin, show all data
            queryset = (
                queryset.annotate(
                    _order_count=Count("order", distinct=True),
                    _total_spent=Sum("order__grand_total"),
                    _cart_items=Count("cartitem", distinct=True),
                    _reviews_count=Count("reviewrating", distinct=True),
                    _testimonials_count=Count("testimonials", distinct=True),
                    _avg_rating_given=Avg("reviewrating__rating"),
                )
                .select_related()
                .prefetch_related(
                    "order_set", "cartitem_set", "reviewrating_set", "testimonials"
                )
            )
        else:
            # For staff/superusers, only show customer-related metrics for regular customers
            queryset = (
                queryset.annotate(
                    _order_count=Count("order", distinct=True),
                    _total_spent=Sum("order__grand_total"),
                    _cart_items=Count("cartitem", distinct=True),
                    _reviews_count=Count("reviewrating", distinct=True),
                    _testimonials_count=Count("testimonials", distinct=True),
                    _avg_rating_given=Avg("reviewrating__rating"),
                )
                .select_related()
                .prefetch_related(
                    "order_set", "cartitem_set", "reviewrating_set", "testimonials"
                )
            )
        return queryset

    def full_name(self, obj):
        return obj.get_full_name() or "No name provided"

    full_name.short_description = "Full Name"
    full_name.admin_order_field = "first_name"

    def user_type(self, obj):
        if obj.is_superuser:
            return "Superuser"
        elif obj.is_staff:
            return "Staff"
        elif obj.google_id:
            return "Google User"
        else:
            return "Regular User"

    user_type.short_description = "Type"

    def activity_level(self, obj):
        # Only calculate activity for regular customers
        if obj.is_staff or obj.is_superuser:
            return "Staff/Admin"
        order_count = obj._order_count
        if order_count >= 5:
            return "High"
        elif order_count >= 2:
            return "Medium"
        elif order_count >= 1:
            return "Low"
        else:
            return "Inactive"

    activity_level.short_description = "Activity"

    def order_count(self, obj):
        # Only show order count for regular customers
        if obj.is_staff or obj.is_superuser:
            return "N/A (Staff/Admin)"
        return obj._order_count

    order_count.short_description = "Orders"

    def total_spent(self, obj):
        # Only show spending for regular customers
        if obj.is_staff or obj.is_superuser:
            return "N/A (Staff/Admin)"
        total = obj._total_spent or 0
        return f"Rs. {total:,}"

    total_spent.short_description = "Total Spent"

    def user_summary(self, obj):
        summary = f"""
User Overview:
Full Name: {obj.get_full_name() or "Not provided"}
Email: {obj.email}
Account Type: {"Google Account" if obj.google_id else "Regular Account"}
{" | Staff" if obj.is_staff else ""}
{" | Superuser" if obj.is_superuser else ""}
Status: {"Active" if obj.is_active else "Inactive"}
Last Login: {obj.last_login.strftime("%B %d, %Y at %I:%M %p") if obj.last_login else "Never"}
User ID: {obj.id}
        """
        return summary.strip()

    user_summary.short_description = "User Summary"

    def order_history(self, obj):
        # Only show order history for regular customers
        if obj.is_staff or obj.is_superuser:
            return "Staff/Admin accounts don't place orders"
        
        orders = obj.order_set.all()[:10]
        if not orders:
            return "No orders placed"

        history = []
        total_spent = 0
        for order in orders:
            total_spent += order.grand_total
            history.append(f"{order.order_number} - {order.status} - Rs. {order.grand_total:,} - {order.created_at.strftime('%b %d, %Y')}")

        if obj.order_set.count() > 10:
            history.append(f"... and {obj.order_set.count() - 10} more orders")

        history.append(f"Total Orders: {obj.order_set.count()} | Total Spent: Rs. {total_spent:,}")
        return "\n".join(history)

    order_history.short_description = "Order History"

    def cart_summary(self, obj):
        # Only show cart for regular customers
        if obj.is_staff or obj.is_superuser:
            return "Staff/Admin accounts don't have shopping carts"
        
        cart_items = obj.cartitem_set.filter(is_active=True)
        if not cart_items:
            return "No active cart items"

        cart_info = []
        total_value = 0
        for item in cart_items[:5]:
            item_total = item.sub_total()
            total_value += item_total
            cart_info.append(f"{item.product.name} x{item.quantity} - Rs. {item_total:,.2f}")

        if cart_items.count() > 5:
            cart_info.append(f"... and {cart_items.count() - 5} more items")

        cart_info.append(f"Total Items: {cart_items.count()} | Cart Value: Rs. {total_value:,.2f}")
        return "\n".join(cart_info)

    cart_summary.short_description = "Current Cart"

    def review_summary(self, obj):
        # Only show reviews for regular customers
        if obj.is_staff or obj.is_superuser:
            return "Staff/Admin accounts don't write customer reviews"
        
        reviews = obj.reviewrating_set.all()
        if not reviews:
            return "No reviews written"

        avg_rating = obj._avg_rating_given or 0
        return f"Total Reviews: {reviews.count()}\nAverage Rating: {avg_rating:.1f}/5\nLatest Review: {reviews.first().created_at.strftime('%b %d, %Y') if reviews.exists() else 'None'}"

    review_summary.short_description = "Review Activity"

    def testimonial_summary(self, obj):
        # Only show testimonials for regular customers
        if obj.is_staff or obj.is_superuser:
            return "Staff/Admin accounts don't write customer testimonials"
        
        testimonials = obj.testimonials.all()
        if not testimonials:
            return "No testimonials"

        featured_testimonials = testimonials.filter(is_featured=True)
        avg_rating = testimonials.aggregate(avg_rating=Avg('rating'))['avg_rating'] or 0

        return f"Total Testimonials: {testimonials.count()}\nFeatured: {featured_testimonials.count()}\nAverage Rating: {avg_rating:.1f}/5\nLatest: {testimonials.first().created_at.strftime('%b %d, %Y') if testimonials.exists() else 'None'}"

    testimonial_summary.short_description = "Testimonials"

    # Custom actions
    def activate_users(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f"{updated} users activated.")

    activate_users.short_description = "Activate selected users"

    def deactivate_users(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f"{updated} users deactivated.")

    deactivate_users.short_description = "Deactivate selected users"

    def make_staff(self, request, queryset):
        updated = queryset.update(is_staff=True)
        self.message_user(request, f"{updated} users granted staff access.")

    make_staff.short_description = "Grant staff access to selected users"

    def remove_staff(self, request, queryset):
        updated = queryset.filter(is_superuser=False).update(is_staff=False)
        self.message_user(request, f"{updated} users had staff access removed.")

    remove_staff.short_description = "Remove staff access from selected users"


class CustomerAccountAdmin(BaseUserAccountAdmin):
    """Admin interface for regular customer accounts (non-staff, non-superuser)"""

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.filter(is_staff=False, is_superuser=False)

    actions = ["activate_users", "deactivate_users"]


class StaffAccountAdmin(BaseUserAccountAdmin):
    """Admin interface for staff accounts (staff but not superuser)"""

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.filter(is_staff=True, is_superuser=False)

    def has_add_permission(self, request):
        return request.user.is_superuser

    def has_change_permission(self, request, obj=None):
        return request.user.is_superuser

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser

    actions = ["activate_users", "deactivate_users", "remove_staff"]


class SuperuserAccountAdmin(BaseUserAccountAdmin):
    """Admin interface for superuser accounts"""

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.filter(is_superuser=True)

    def has_add_permission(self, request):
        return request.user.is_superuser

    def has_change_permission(self, request, obj=None):
        return request.user.is_superuser

    def has_delete_permission(self, request, obj=None):
        if obj and obj == request.user:
            return False
        return request.user.is_superuser

    actions = ["activate_users", "deactivate_users"]


# Register the separate admin classes
admin.site.register(UserAccount, CustomerAccountAdmin)
admin.site.register(StaffUserAccount, StaffAccountAdmin)
admin.site.register(SuperuserAccount, SuperuserAccountAdmin)
