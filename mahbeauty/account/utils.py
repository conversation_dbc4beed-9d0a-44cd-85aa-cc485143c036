"""
Utility functions for account management, including image handling
"""

import os
import requests
from io import BytesIO
from PIL import Image
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


def download_image_from_url(url, max_size_mb=5):
    """
    Download an image from a URL and return it as a ContentFile.
    
    Args:
        url (str): The URL of the image to download
        max_size_mb (int): Maximum file size in MB (default: 5MB)
    
    Returns:
        ContentFile or None: The downloaded image as a ContentFile, or None if failed
    """
    if not url:
        return None
    
    try:
        # Set headers to mimic a browser request
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        # Download the image with timeout
        response = requests.get(url, headers=headers, timeout=10, stream=True)
        response.raise_for_status()
        
        # Check content type
        content_type = response.headers.get('content-type', '').lower()
        if not content_type.startswith('image/'):
            logger.warning(f"URL does not point to an image: {url}")
            return None
        
        # Check file size
        content_length = response.headers.get('content-length')
        if content_length:
            size_mb = int(content_length) / (1024 * 1024)
            if size_mb > max_size_mb:
                logger.warning(f"Image too large ({size_mb:.1f}MB): {url}")
                return None
        
        # Read the image data
        image_data = BytesIO()
        for chunk in response.iter_content(chunk_size=8192):
            image_data.write(chunk)
            # Check size during download
            if image_data.tell() > max_size_mb * 1024 * 1024:
                logger.warning(f"Image download exceeded size limit: {url}")
                return None
        
        image_data.seek(0)
        
        # Validate that it's actually an image
        try:
            with Image.open(image_data) as img:
                img.verify()
        except Exception as e:
            logger.warning(f"Invalid image format: {url} - {e}")
            return None
        
        image_data.seek(0)
        
        # Determine file extension from content type
        extension_map = {
            'image/jpeg': '.jpg',
            'image/jpg': '.jpg',
            'image/png': '.png',
            'image/gif': '.gif',
            'image/webp': '.webp'
        }
        extension = extension_map.get(content_type, '.jpg')
        
        # Create a ContentFile
        return ContentFile(image_data.getvalue(), name=f'profile_image{extension}')
        
    except requests.RequestException as e:
        logger.error(f"Failed to download image from {url}: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error downloading image from {url}: {e}")
        return None


def resize_profile_image(image_file, max_width=300, max_height=300, quality=85):
    """
    Resize a profile image to fit within the specified dimensions while maintaining aspect ratio.
    
    Args:
        image_file: Django file object or ContentFile
        max_width (int): Maximum width in pixels
        max_height (int): Maximum height in pixels
        quality (int): JPEG quality (1-100)
    
    Returns:
        ContentFile: Resized image as ContentFile
    """
    try:
        # Open the image
        with Image.open(image_file) as img:
            # Convert to RGB if necessary (for JPEG compatibility)
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # Calculate new dimensions maintaining aspect ratio
            img.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
            
            # Save to BytesIO
            output = BytesIO()
            img.save(output, format='JPEG', quality=quality, optimize=True)
            output.seek(0)
            
            return ContentFile(output.getvalue(), name='profile_image.jpg')
            
    except Exception as e:
        logger.error(f"Failed to resize image: {e}")
        return image_file  # Return original if resize fails


def save_profile_image_from_url(user, image_url):
    """
    Download and save a profile image from a URL for a user.
    
    Args:
        user: UserAccount instance
        image_url (str): URL of the image to download
    
    Returns:
        bool: True if successful, False otherwise
    """
    if not image_url or user.has_profile_image():
        # Don't overwrite existing profile images
        return False
    
    try:
        # Download the image
        image_file = download_image_from_url(image_url)
        if not image_file:
            return False
        
        # Resize the image
        resized_image = resize_profile_image(image_file)
        
        # Generate a unique filename
        filename = f"user_{user.id}_profile.jpg"
        
        # Save to the user's profile_image field
        user.profile_image.save(filename, resized_image, save=True)
        
        logger.info(f"Successfully saved profile image for user {user.id} from {image_url}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to save profile image for user {user.id} from {image_url}: {e}")
        return False


def get_social_profile_image_url(backend_name, extra_data):
    """
    Extract profile image URL from social authentication extra data.
    
    Args:
        backend_name (str): Name of the social auth backend ('google-oauth2', 'facebook', etc.)
        extra_data (dict): Extra data from social authentication
    
    Returns:
        str or None: Profile image URL if found, None otherwise
    """
    if not extra_data:
        return None
    
    if backend_name == 'google-oauth2':
        # Google provides 'picture' field
        return extra_data.get('picture')
    
    elif backend_name == 'facebook':
        # Facebook provides picture in nested structure
        picture_data = extra_data.get('picture', {})
        if isinstance(picture_data, dict):
            data = picture_data.get('data', {})
            if isinstance(data, dict):
                return data.get('url')
        return picture_data if isinstance(picture_data, str) else None
    
    # Add support for other providers as needed
    return None


def cleanup_old_profile_image(user):
    """
    Clean up old profile image file when user uploads a new one.
    
    Args:
        user: UserAccount instance
    """
    try:
        if user.profile_image:
            # Delete the old file from storage
            if default_storage.exists(user.profile_image.name):
                default_storage.delete(user.profile_image.name)
                logger.info(f"Deleted old profile image for user {user.id}")
    except Exception as e:
        logger.error(f"Failed to cleanup old profile image for user {user.id}: {e}")
