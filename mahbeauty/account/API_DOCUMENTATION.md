# Account App API Documentation

This document describes the REST API endpoints for the account app, which supplements the existing Djoser authentication system with additional user management features.

## 📋 Overview

The account app provides **supplementary** endpoints to the existing Djoser authentication system:

### 🔐 **Djoser Authentication Endpoints** (Primary)
- **User Registration**: `/api/auth/users/` (supports profile image upload)
- **User Login**: `/api/auth/jwt/create/`
- **User Profile**: `/api/auth/users/me/` (includes profile image data)
- **Password Reset**: `/api/auth/users/reset_password/`
- **Google OAuth**: `/api/auth/o/google-oauth2/` (automatically fetches profile image)

### 📊 **Account App Endpoints** (Supplementary)
- **User Management**: Extended user profile and activity management
- **User Statistics**: Detailed analytics and activity summaries
- **User Activity**: Orders, reviews, cart history

## 🔗 Base URL

All supplementary endpoints are prefixed with `/api/account/`

## 📊 Models & Endpoints

### 1. User Account Management API

**Base URL**: `/api/account/users/`

#### Endpoints

| Method | Endpoint | Description | Permission |
|--------|----------|-------------|------------|
| GET | `/api/account/users/` | List users (staff only) | Staff |
| GET | `/api/account/users/{id}/` | Get user profile | Self or Staff |
| PUT | `/api/account/users/{id}/` | Update user profile | Self or Staff |
| PATCH | `/api/account/users/{id}/` | Partial update profile | Self or Staff |

#### Custom Actions

| Method | Endpoint | Description | Permission |
|--------|----------|-------------|------------|
| GET | `/api/account/users/me/` | Get current user profile | Authenticated |
| GET | `/api/account/users/my_activity/` | Get current user activity | Authenticated |
| GET | `/api/account/users/my_stats/` | Get current user statistics | Authenticated |
| GET | `/api/account/users/{id}/activity/` | Get user activity summary | Self or Staff |
| GET | `/api/account/users/{id}/stats/` | Get detailed user statistics | Self or Staff |
| GET | `/api/account/users/{id}/orders/` | Get user's orders | Self or Staff |
| GET | `/api/account/users/{id}/reviews/` | Get user's reviews | Self or Staff |
| GET | `/api/account/users/{id}/cart/` | Get user's cart items | Self or Staff |

## 📝 Serializers & Response Examples

### UserProfileSerializer
```json
{
    "id": 1,
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "full_name": "John Doe",
    "is_google_user": false,
    "account_type": "regular",
    "is_active": true,
    "last_login": "2024-01-01T12:00:00Z"
}
```

### UserActivitySerializer
```json
{
    "id": 1,
    "email": "<EMAIL>",
    "full_name": "John Doe",
    "order_count": 5,
    "total_spent": 1250.00,
    "cart_items_count": 3,
    "reviews_count": 8,
    "testimonials_count": 2,
    "average_rating_given": 4.2,
    "last_login": "2024-01-01T12:00:00Z"
}
```

### UserStatsSerializer (Detailed Statistics)
```json
{
    "orders": {
        "total_orders": 5,
        "completed_orders": 4,
        "pending_orders": 1,
        "cancelled_orders": 0,
        "total_spent": 1250.00,
        "recent_orders": 2,
        "average_order_value": 250.00
    },
    "reviews": {
        "total_reviews": 8,
        "approved_reviews": 7,
        "pending_reviews": 1,
        "average_rating_given": 4.2,
        "recent_reviews": 3
    },
    "cart": {
        "active_cart_items": 3,
        "cart_total_value": 150.00,
        "unique_products_in_cart": 3
    },
    "testimonials": {
        "total_testimonials": 2,
        "approved_testimonials": 2,
        "featured_testimonials": 1,
        "average_testimonial_rating": 5.0
    }
}
```

## 🔍 Filtering & Search

### User Management
- **Search**: `email`, `first_name`, `last_name`
- **Filters**: `is_active`, `is_staff`
- **Ordering**: `email`, `first_name`, `last_name`, `last_login`

## 🔐 Permissions

### Access Control
- **Self Access**: Users can view and edit their own profiles and data
- **Staff Access**: Staff users can view and manage all user accounts
- **Anonymous Access**: No access to account endpoints (authentication required)

### Permission Matrix

| Action | Self | Staff | Anonymous |
|--------|------|-------|-----------|
| View Profile | ✅ | ✅ | ❌ |
| Edit Profile | ✅ | ✅ | ❌ |
| View Activity | ✅ | ✅ | ❌ |
| View Statistics | ✅ | ✅ | ❌ |
| View Orders | ✅ | ✅ | ❌ |
| View Reviews | ✅ | ✅ | ❌ |
| View Cart | ✅ | ✅ | ❌ |
| List All Users | ❌ | ✅ | ❌ |

## 📝 Usage Examples

### Get Current User Profile
```bash
GET /api/account/users/me/
Authorization: Bearer <access_token>
```

### Get Current User Activity Summary
```bash
GET /api/account/users/my_activity/
Authorization: Bearer <access_token>
```

### Get Detailed User Statistics
```bash
GET /api/account/users/my_stats/
Authorization: Bearer <access_token>
```

### Update User Profile
```bash
PATCH /api/account/users/me/
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "first_name": "John",
    "last_name": "Smith"
}
```

### Get User's Orders (with status filter)
```bash
GET /api/account/users/me/orders/?status=Completed
Authorization: Bearer <access_token>
```

### Get User's Reviews (with status filter)
```bash
GET /api/account/users/me/reviews/?status=true
Authorization: Bearer <access_token>
```

## 🔄 Integration with Djoser

### Primary Authentication Flow
1. **Registration**: Use `/api/auth/users/` (Djoser)
2. **Login**: Use `/api/auth/jwt/create/` (Djoser)
3. **Profile Management**: Use `/api/account/users/me/` (Account App)
4. **Activity Tracking**: Use `/api/account/users/my_activity/` (Account App)

### Recommended Usage Pattern
```javascript
// 1. Login with Djoser
const loginResponse = await fetch('/api/auth/jwt/create/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password })
});
const { access, refresh } = await loginResponse.json();

// 2. Get extended profile with Account App
const profileResponse = await fetch('/api/account/users/me/', {
    headers: { 'Authorization': `Bearer ${access}` }
});
const profile = await profileResponse.json();

// 3. Get user activity summary
const activityResponse = await fetch('/api/account/users/my_activity/', {
    headers: { 'Authorization': `Bearer ${access}` }
});
const activity = await activityResponse.json();
```

## 🚨 Error Handling

The API returns appropriate HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `401`: Unauthorized
- `403`: Forbidden (permission denied)
- `404`: Not Found
- `500`: Internal Server Error

### Permission Denied Example
```json
{
    "error": "Permission denied"
}
```

### Validation Error Example
```json
{
    "first_name": ["First name cannot be empty"]
}
```

## 🖼️ Profile Image Management

### **Profile Image Upload**

Users can upload profile images during registration or update their profile later.

#### **During Registration**
```http
POST /api/auth/users/
Content-Type: multipart/form-data

{
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "password": "securepassword123",
    "re_password": "securepassword123",
    "profile_image": [image file]
}
```

#### **Update Profile Image**
```http
PATCH /api/auth/users/me/
Content-Type: multipart/form-data
Authorization: Bearer your_jwt_token

{
    "profile_image": [image file]
}
```

#### **Remove Profile Image**
```http
PATCH /api/auth/users/me/
Content-Type: application/json
Authorization: Bearer your_jwt_token

{
    "profile_image": null
}
```

### **Profile Image Response Fields**

When retrieving user profile data, the following image-related fields are included:

```json
{
    "id": 1,
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "full_name": "John Doe",
    "profile_image": "/media/profile_images/user_1_profile.jpg",
    "profile_image_url": "http://localhost:8000/media/profile_images/user_1_profile.jpg",
    "has_profile_image": true,
    "is_google_user": false,
    "account_type": "Regular User",
    "is_active": true,
    "last_login": "2024-01-15T10:30:00Z"
}
```

### **Social Authentication Profile Images**

When users authenticate via Google OAuth or Facebook, their profile images are automatically downloaded and saved:

- **Google OAuth**: Profile image is fetched from the `picture` field
- **Facebook**: Profile image is fetched from the user's profile picture
- Images are automatically resized to 300x300 pixels maximum
- Only saved if the user doesn't already have a profile image

### **Image Processing Features**

- **Automatic Resizing**: Images are resized to fit within 300x300 pixels while maintaining aspect ratio
- **Format Conversion**: All images are converted to JPEG format for consistency
- **Size Validation**: Maximum file size of 5MB
- **Format Validation**: Supports JPEG, PNG, GIF, and WebP formats
- **Quality Optimization**: Images are compressed with 85% quality for optimal file size

## 📋 Summary

The Account App provides **supplementary functionality** to Djoser:

### ✅ **Use Djoser For:**
- User registration and authentication
- Password management
- Social authentication (Google OAuth)
- Basic profile operations

### ✅ **Use Account App For:**
- Extended user profiles with additional information
- User activity summaries and statistics
- Detailed analytics (orders, reviews, cart history)
- User management for staff/admin interfaces

This separation allows you to leverage Djoser's robust authentication while adding e-commerce specific user management features.
