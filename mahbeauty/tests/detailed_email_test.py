#!/usr/bin/env python3
"""
Detailed email diagnostic script
"""

import os
import sys
import django
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# Setup Django first before importing any Django modules
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "mahbeauty.settings")
django.setup()

from django.conf import settings
from django.core.mail import send_mail, EmailMultiAlternatives
from orders.utils import get_admin_emails


def test_smtp_connection():
    """Test direct SMTP connection"""
    print("🔌 TESTING DIRECT SMTP CONNECTION")
    print("-" * 40)
    
    try:
        # Create SMTP connection
        server = smtplib.SMTP(settings.EMAIL_HOST, settings.EMAIL_PORT)
        server.set_debuglevel(1)  # Enable debug output
        
        if settings.EMAIL_USE_TLS:
            server.starttls()
        
        # Login
        server.login(settings.EMAIL_HOST_USER, settings.EMAIL_HOST_PASSWORD)
        print("✅ SMTP connection and authentication successful")
        
        # Test sending a simple email
        admin_emails = get_admin_emails()
        if admin_emails:
            msg = MIMEText("Direct SMTP test from MahBeauty")
            msg['Subject'] = "Direct SMTP Test"
            msg['From'] = settings.EMAIL_HOST_USER
            msg['To'] = admin_emails[0]
            
            server.send_message(msg)
            print(f"✅ Direct SMTP email sent to {admin_emails[0]}")
        
        server.quit()
        return True
        
    except Exception as e:
        print(f"❌ SMTP connection failed: {e}")
        return False


def test_django_email_with_details():
    """Test Django email with detailed logging"""
    print("📧 TESTING DJANGO EMAIL WITH DETAILS")
    print("-" * 40)
    
    admin_emails = get_admin_emails()
    if not admin_emails:
        print("❌ No admin emails configured")
        return False
    
    try:
        # Test with different from addresses
        test_cases = [
            {
                'from_email': settings.EMAIL_HOST_USER,
                'subject': 'Test 1: From EMAIL_HOST_USER',
                'description': 'Using EMAIL_HOST_USER as sender'
            },
            {
                'from_email': settings.DEFAULT_FROM_EMAIL,
                'subject': 'Test 2: From DEFAULT_FROM_EMAIL', 
                'description': 'Using DEFAULT_FROM_EMAIL as sender'
            },
            {
                'from_email': f"MahBeauty <{settings.EMAIL_HOST_USER}>",
                'subject': 'Test 3: With Display Name',
                'description': 'Using display name with email'
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📨 Test {i}: {test_case['description']}")
            print(f"   From: {test_case['from_email']}")
            print(f"   To: {admin_emails[0]}")
            print(f"   Subject: {test_case['subject']}")
            
            try:
                result = send_mail(
                    subject=test_case['subject'],
                    message=f"This is test email #{i} from MahBeauty.\n\n"
                           f"Configuration:\n"
                           f"- EMAIL_HOST: {settings.EMAIL_HOST}\n"
                           f"- EMAIL_HOST_USER: {settings.EMAIL_HOST_USER}\n"
                           f"- From Address: {test_case['from_email']}\n"
                           f"- To Address: {admin_emails[0]}\n\n"
                           f"If you receive this email, the configuration is working!",
                    from_email=test_case['from_email'],
                    recipient_list=admin_emails,
                    fail_silently=False,
                )
                print(f"   ✅ Sent successfully (result: {result})")
                
            except Exception as e:
                print(f"   ❌ Failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Django email test failed: {e}")
        return False


def check_email_server_reputation():
    """Check if email server might have reputation issues"""
    print("🔍 EMAIL SERVER ANALYSIS")
    print("-" * 30)
    
    print(f"📧 Email Server: {settings.EMAIL_HOST}")
    print(f"👤 Sender: {settings.EMAIL_HOST_USER}")
    print(f"📬 Admin Email: {get_admin_emails()[0] if get_admin_emails() else 'None'}")
    print()
    
    print("🚨 POTENTIAL ISSUES TO CHECK:")
    print("1. Check spam folder in Gmail")
    print("2. Verify mail.mah-beauty.com is properly configured")
    print("3. Check if your domain has proper SPF/DKIM records")
    print("4. Verify the admin email address is correct")
    print("5. Check if there are any email filters in Gmail")
    print()
    
    print("💡 RECOMMENDATIONS:")
    print("- Add <EMAIL> to Gmail contacts")
    print("- Check Gmail's 'All Mail' folder")
    print("- Look for emails from '<EMAIL>'")
    print("- Verify your custom mail server is working properly")


def main():
    print("🔬 DETAILED EMAIL DIAGNOSTIC")
    print("=" * 50)
    
    # Test SMTP connection
    smtp_result = test_smtp_connection()
    print()
    
    # Test Django email
    django_result = test_django_email_with_details()
    print()
    
    # Analysis
    check_email_server_reputation()
    print()
    
    # Summary
    print("📊 DIAGNOSTIC SUMMARY")
    print("-" * 25)
    print(f"SMTP Connection: {'✅ PASS' if smtp_result else '❌ FAIL'}")
    print(f"Django Email: {'✅ PASS' if django_result else '❌ FAIL'}")
    
    if smtp_result and django_result:
        print("\n🎯 CONCLUSION:")
        print("Email system is working correctly from Django's side.")
        print("If you're not receiving emails, check:")
        print("1. Gmail spam folder")
        print("2. Gmail filters")
        print("3. 'All Mail' folder in Gmail")
        print("4. Email server reputation/configuration")
    else:
        print("\n🚨 ISSUES DETECTED:")
        print("There are problems with the email configuration.")


if __name__ == "__main__":
    main()
