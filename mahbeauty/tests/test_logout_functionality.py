#!/usr/bin/env python3
"""
Test script to verify JWT token blacklist and logout functionality
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_user_registration():
    """Test user registration"""
    url = f"{BASE_URL}/auth/users/"
    data = {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "re_password": "testpassword123",
        "first_name": "Test",
        "last_name": "Logout"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Registration Status: {response.status_code}")
        if response.status_code == 201:
            print("✅ User registered successfully")
            return True
        elif response.status_code == 400:
            error_data = response.json()
            if "email" in error_data and "already exists" in str(error_data["email"]):
                print("✅ User already exists, proceeding with login test")
                return True
        print(f"❌ Registration failed: {response.json()}")
        return False
    except Exception as e:
        print(f"❌ Registration Error: {e}")
        return False

def test_user_login():
    """Test user login and get tokens"""
    url = f"{BASE_URL}/auth/jwt/create/"
    data = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Login Status: {response.status_code}")
        
        if response.status_code == 200:
            tokens = response.json()
            print("✅ Login successful")
            print(f"Access Token: {tokens['access'][:50]}...")
            print(f"Refresh Token: {tokens['refresh'][:50]}...")
            return tokens.get('access'), tokens.get('refresh')
        else:
            print(f"❌ Login failed: {response.json()}")
            return None, None
    except Exception as e:
        print(f"❌ Login Error: {e}")
        return None, None

def test_protected_endpoint(access_token):
    """Test accessing protected endpoint with access token"""
    url = f"{BASE_URL}/auth/users/me/"
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Profile Access Status: {response.status_code}")
        
        if response.status_code == 200:
            profile = response.json()
            print(f"✅ Profile accessed: {profile['email']}")
            return True
        else:
            print(f"❌ Profile access failed: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Profile Error: {e}")
        return False

def test_token_refresh(refresh_token):
    """Test token refresh functionality"""
    url = f"{BASE_URL}/auth/jwt/refresh/"
    data = {
        "refresh": refresh_token
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Token Refresh Status: {response.status_code}")
        
        if response.status_code == 200:
            new_tokens = response.json()
            print("✅ Token refresh successful")
            print(f"New Access Token: {new_tokens['access'][:50]}...")
            if 'refresh' in new_tokens:
                print(f"New Refresh Token: {new_tokens['refresh'][:50]}...")
                return new_tokens['access'], new_tokens['refresh']
            return new_tokens['access'], refresh_token
        else:
            print(f"❌ Token refresh failed: {response.json()}")
            return None, None
    except Exception as e:
        print(f"❌ Token refresh Error: {e}")
        return None, None

def test_logout_blacklist(refresh_token):
    """Test logout functionality with token blacklisting"""
    url = f"{BASE_URL}/auth/jwt/blacklist/"
    data = {
        "refresh": refresh_token
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Logout/Blacklist Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Token blacklisted successfully (logout successful)")
            return True
        else:
            print(f"❌ Logout failed: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Logout Error: {e}")
        return False

def test_blacklisted_token_usage(blacklisted_refresh_token):
    """Test that blacklisted refresh token cannot be used"""
    url = f"{BASE_URL}/auth/jwt/refresh/"
    data = {
        "refresh": blacklisted_refresh_token
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Blacklisted Token Usage Status: {response.status_code}")
        
        if response.status_code == 401:
            print("✅ Blacklisted token correctly rejected")
            return True
        else:
            print(f"❌ Blacklisted token was accepted (security issue!): {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Blacklisted token test Error: {e}")
        return False

def main():
    """Run all logout functionality tests"""
    print("🧪 Testing JWT Token Blacklist and Logout Functionality")
    print("=" * 60)
    
    # Test 1: Registration
    print("\n1. Testing User Registration...")
    if not test_user_registration():
        return
    
    # Test 2: Login
    print("\n2. Testing User Login...")
    access_token, refresh_token = test_user_login()
    if not access_token or not refresh_token:
        return
    
    # Test 3: Protected endpoint access
    print("\n3. Testing Protected Endpoint Access...")
    if not test_protected_endpoint(access_token):
        return
    
    # Test 4: Token refresh
    print("\n4. Testing Token Refresh...")
    new_access_token, new_refresh_token = test_token_refresh(refresh_token)
    if not new_access_token:
        return
    
    # Test 5: Logout (blacklist token)
    print("\n5. Testing Logout (Token Blacklisting)...")
    if not test_logout_blacklist(new_refresh_token):
        return
    
    # Test 6: Try to use blacklisted token
    print("\n6. Testing Blacklisted Token Rejection...")
    if not test_blacklisted_token_usage(new_refresh_token):
        return
    
    print("\n" + "=" * 60)
    print("🎉 All logout functionality tests passed!")
    print("✅ JWT token blacklist is working correctly")
    print("✅ Logout functionality is properly implemented")

if __name__ == "__main__":
    main()
