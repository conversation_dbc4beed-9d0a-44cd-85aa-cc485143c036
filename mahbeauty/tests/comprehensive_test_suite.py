#!/usr/bin/env python3
"""
Comprehensive Test Suite for MahBeauty Django Application

This script consolidates all test functionality into a single file:
- Account linking tests
- Authentication endpoint tests
- Email conflict tests
- Facebook linking tests
- Logout functionality tests
- Profile image tests
- Admin navigation tests
- Social auth test data creation

Usage:
    python comprehensive_test_suite.py --all
    python comprehensive_test_suite.py --auth
    python comprehensive_test_suite.py --social
    python comprehensive_test_suite.py --admin
    python comprehensive_test_suite.py --profile
    python comprehensive_test_suite.py --create-data
"""

import os
import sys
import django
import argparse
import requests
import json
from io import BytesIO
from PIL import Image
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mahbeauty.settings')
django.setup()

from django.utils import timezone
from django.core.files.uploadedfile import SimpleUploadedFile
from account.models import UserAccount
from social_django.models import User<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Association
from account.social_auth_pipeline import associate_by_email, create_user_with_social_data
from account.utils import (
    download_image_from_url, 
    resize_profile_image, 
    save_profile_image_from_url,
    get_social_profile_image_url
)

# Configuration
BASE_URL = "http://127.0.0.1:8000"
API_BASE_URL = f"{BASE_URL}/api"

class Colors:
    """ANSI color codes for terminal output"""
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_header(title, char="=", width=60):
    """Print a formatted header"""
    print(f"\n{Colors.BOLD}{Colors.BLUE}{char * width}{Colors.END}")
    print(f"{Colors.BOLD}{Colors.WHITE}{title.center(width)}{Colors.END}")
    print(f"{Colors.BOLD}{Colors.BLUE}{char * width}{Colors.END}")

def print_success(message):
    """Print success message"""
    print(f"{Colors.GREEN}✅ {message}{Colors.END}")

def print_error(message):
    """Print error message"""
    print(f"{Colors.RED}❌ {message}{Colors.END}")

def print_warning(message):
    """Print warning message"""
    print(f"{Colors.YELLOW}⚠️  {message}{Colors.END}")

def print_info(message):
    """Print info message"""
    print(f"{Colors.CYAN}ℹ️  {message}{Colors.END}")

class AuthenticationTests:
    """Authentication and JWT token tests"""
    
    def __init__(self):
        self.test_email = "<EMAIL>"
        self.test_password = "testpassword123"
    
    def test_user_registration(self):
        """Test user registration with email/password"""
        print_info("Testing user registration...")
        
        url = f"{API_BASE_URL}/auth/users/"
        data = {
            "email": self.test_email,
            "password": self.test_password,
            "re_password": self.test_password,
            "first_name": "Test",
            "last_name": "User"
        }
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 201:
                print_success("User registered successfully")
                return True
            elif response.status_code == 400:
                error_data = response.json()
                if "email" in error_data and "already exists" in str(error_data["email"]):
                    print_success("User already exists, proceeding with tests")
                    return True
            print_error(f"Registration failed: {response.json()}")
            return False
        except Exception as e:
            print_error(f"Registration error: {e}")
            return False
    
    def test_user_login(self):
        """Test user login and get tokens"""
        print_info("Testing user login...")
        
        url = f"{API_BASE_URL}/auth/jwt/create/"
        data = {
            "email": self.test_email,
            "password": self.test_password
        }
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 200:
                tokens = response.json()
                print_success("Login successful")
                return tokens.get('access'), tokens.get('refresh')
            else:
                print_error(f"Login failed: {response.json()}")
                return None, None
        except Exception as e:
            print_error(f"Login error: {e}")
            return None, None
    
    def test_protected_endpoint(self, access_token):
        """Test accessing protected endpoint with JWT token"""
        print_info("Testing protected endpoint access...")
        
        url = f"{API_BASE_URL}/auth/users/me/"
        headers = {"Authorization": f"Bearer {access_token}"}
        
        try:
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                profile = response.json()
                print_success(f"Profile accessed: {profile['email']}")
                return True
            else:
                print_error(f"Profile access failed: {response.json()}")
                return False
        except Exception as e:
            print_error(f"Profile error: {e}")
            return False
    
    def test_token_refresh(self, refresh_token):
        """Test token refresh functionality"""
        print_info("Testing token refresh...")
        
        url = f"{API_BASE_URL}/auth/jwt/refresh/"
        data = {"refresh": refresh_token}
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 200:
                new_tokens = response.json()
                print_success("Token refresh successful")
                return new_tokens['access'], new_tokens.get('refresh', refresh_token)
            else:
                print_error(f"Token refresh failed: {response.json()}")
                return None, None
        except Exception as e:
            print_error(f"Token refresh error: {e}")
            return None, None
    
    def test_logout_blacklist(self, refresh_token):
        """Test logout functionality with token blacklisting"""
        print_info("Testing logout (token blacklisting)...")
        
        url = f"{API_BASE_URL}/auth/jwt/blacklist/"
        data = {"refresh": refresh_token}
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 200:
                print_success("Token blacklisted successfully (logout successful)")
                return True
            else:
                print_error(f"Logout failed: {response.json()}")
                return False
        except Exception as e:
            print_error(f"Logout error: {e}")
            return False
    
    def test_blacklisted_token_usage(self, blacklisted_refresh_token):
        """Test that blacklisted refresh token cannot be used"""
        print_info("Testing blacklisted token rejection...")
        
        url = f"{API_BASE_URL}/auth/jwt/refresh/"
        data = {"refresh": blacklisted_refresh_token}
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 401:
                print_success("Blacklisted token correctly rejected")
                return True
            else:
                print_error(f"Blacklisted token was accepted (security issue!)")
                return False
        except Exception as e:
            print_error(f"Blacklisted token test error: {e}")
            return False
    
    def run_all_tests(self):
        """Run all authentication tests"""
        print_header("Authentication & JWT Token Tests")
        
        results = []
        
        # Test registration
        results.append(("Registration", self.test_user_registration()))
        
        # Test login
        access_token, refresh_token = self.test_user_login()
        results.append(("Login", bool(access_token and refresh_token)))
        
        if access_token and refresh_token:
            # Test protected endpoint
            results.append(("Protected Access", self.test_protected_endpoint(access_token)))
            
            # Test token refresh
            new_access_token, new_refresh_token = self.test_token_refresh(refresh_token)
            results.append(("Token Refresh", bool(new_access_token)))
            
            if new_refresh_token:
                # Test logout
                results.append(("Logout", self.test_logout_blacklist(new_refresh_token)))
                
                # Test blacklisted token
                results.append(("Blacklist Check", self.test_blacklisted_token_usage(new_refresh_token)))
        
        return results

class SocialAuthTests:
    """Social authentication and account linking tests"""
    
    def test_account_linking(self):
        """Test Google account linking functionality"""
        print_info("Testing Google account linking...")
        
        test_email = "<EMAIL>"
        
        # Clean up
        UserAccount.objects.filter(email=test_email).delete()
        
        # Create user via Djoser
        djoser_user = UserAccount.objects.create_user(
            email=test_email,
            password="testpassword123",
            first_name="John",
            last_name="EmailUser"
        )
        
        # Mock Google OAuth
        class MockBackend:
            name = 'google-oauth2'
        
        backend = MockBackend()
        details = {
            'email': test_email,
            'first_name': 'John',
            'last_name': 'GoogleUser'
        }
        kwargs = {'uid': '***************'}
        
        # Test pipeline function
        result = associate_by_email(
            backend=backend,
            details=details,
            user=None,
            **kwargs
        )
        
        if result and result.get('user'):
            linked_user = result['user']
            
            # Create social auth record
            UserSocialAuth.objects.get_or_create(
                user=linked_user,
                provider='google-oauth2',
                uid=kwargs['uid'],
                defaults={'extra_data': details}
            )
            
            # Verify results
            all_users = UserAccount.objects.filter(email=test_email)
            if all_users.count() == 1:
                print_success("Google account linking successful")
                return True
        
        print_error("Google account linking failed")
        return False
    
    def test_facebook_linking(self):
        """Test Facebook account linking functionality"""
        print_info("Testing Facebook account linking...")
        
        test_email = "<EMAIL>"
        
        # Clean up
        UserAccount.objects.filter(email=test_email).delete()
        
        # Create user via Djoser
        user = UserAccount.objects.create_user(
            email=test_email,
            password="testpassword123",
            first_name="Sarah",
            last_name="EmailUser"
        )
        
        # Create Facebook social auth
        try:
            UserSocialAuth.objects.create(
                user=user,
                provider='facebook',
                uid='facebook_123456789',
                extra_data={'email': test_email, 'name': 'Sarah FacebookUser'}
            )
            print_success("Facebook account linking successful")
            return True
        except Exception as e:
            print_error(f"Facebook linking failed: {e}")
            return False
    
    def test_email_conflict(self):
        """Test email conflict handling"""
        print_info("Testing email conflict scenarios...")
        
        test_email = "<EMAIL>"
        
        # Clean up
        UserAccount.objects.filter(email=test_email).delete()
        
        # Create Djoser user
        djoser_user = UserAccount.objects.create_user(
            email=test_email,
            password="testpassword123",
            first_name="John",
            last_name="Djoser"
        )
        
        # Try to create Google user with same email
        try:
            google_user = UserAccount.objects.create_user(
                email=test_email,
                first_name="John",
                last_name="Google"
            )
            
            all_users = UserAccount.objects.filter(email=test_email)
            if all_users.count() > 1:
                print_warning("Multiple accounts with same email detected")
                return False
            else:
                print_success("Email conflict handled correctly")
                return True
                
        except Exception as e:
            print_success("Email uniqueness enforced correctly")
            return True
    
    def run_all_tests(self):
        """Run all social authentication tests"""
        print_header("Social Authentication & Account Linking Tests")
        
        results = []
        results.append(("Google Linking", self.test_account_linking()))
        results.append(("Facebook Linking", self.test_facebook_linking()))
        results.append(("Email Conflict", self.test_email_conflict()))
        
        return results
