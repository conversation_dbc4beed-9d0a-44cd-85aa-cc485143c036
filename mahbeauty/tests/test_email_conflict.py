#!/usr/bin/env python3
"""
Test what happens when same email is used for both Djoser and Google OAuth
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mahbeauty.settings')
django.setup()

from account.models import UserAccount
from social_django.models import UserSocialAuth

def test_email_conflict_scenario():
    """Test what happens with same email in different auth methods"""
    
    print("🧪 Testing Email Conflict Between Djoser and Google OAuth")
    print("="*60)
    
    test_email = "<EMAIL>"
    
    # Clean up any existing test data
    UserAccount.objects.filter(email=test_email).delete()
    print(f"🧹 Cleaned up existing test data for {test_email}")
    
    print(f"\n📧 Testing with email: {test_email}")
    
    # Scenario 1: Create user via <PERSON>jos<PERSON> (email/password)
    print(f"\n1️⃣ Creating user via <PERSON>joser (email/password)...")
    
    djoser_user = UserAccount.objects.create_user(
        email=test_email,
        password="testpassword123",
        first_name="<PERSON>",
        last_name="<PERSON><PERSON><PERSON>"
    )
    print(f"   ✅ Djoser user created: ID={djoser_user.id}, Name={djoser_user.get_full_name()}")
    
    # Scenario 2: Try to create user via Google OAuth with same email
    print(f"\n2️⃣ Creating user via Google OAuth with same email...")
    
    try:
        # This simulates what happens when Google OAuth tries to create a user
        google_user = UserAccount.objects.create_user(
            email=test_email,  # Same email!
            first_name="John",
            last_name="Google"
        )
        
        # Create social auth entry
        social_auth = UserSocialAuth.objects.create(
            user=google_user,
            provider='google-oauth2',
            uid='***************',
            extra_data={
                'email': test_email,
                'name': 'John Google',
                'first_name': 'John',
                'last_name': 'Google'
            }
        )
        
        print(f"   ✅ Google user created: ID={google_user.id}, Name={google_user.get_full_name()}")
        print(f"   ✅ Social auth created for Google user")
        
    except Exception as e:
        print(f"   ❌ Error creating Google user: {e}")
        google_user = None
        social_auth = None
    
    # Check results
    print(f"\n📊 RESULTS:")
    print(f"="*30)
    
    all_users = UserAccount.objects.filter(email=test_email)
    print(f"👥 Total users with email {test_email}: {all_users.count()}")
    
    for i, user in enumerate(all_users, 1):
        print(f"   User {i}: ID={user.id}, Name={user.get_full_name()}")
        
        # Check if user has social auth
        social_auths = UserSocialAuth.objects.filter(user=user)
        if social_auths.exists():
            for auth in social_auths:
                print(f"     🔗 Has {auth.provider} social auth (UID: {auth.uid})")
        else:
            print(f"     📧 Email/password only user")
    
    # Check what this means for login
    print(f"\n🔐 LOGIN IMPLICATIONS:")
    print(f"="*25)
    
    if all_users.count() > 1:
        print(f"❌ PROBLEM: Multiple accounts with same email!")
        print(f"   📧 Email/password login → User ID {djoser_user.id}")
        if google_user:
            print(f"   🔵 Google OAuth login → User ID {google_user.id}")
        print(f"   ⚠️  User will have different profiles/data depending on login method!")
    else:
        print(f"✅ Only one account exists - no conflict")
    
    return all_users.count()

def test_solution_approach():
    """Show how to solve the email conflict issue"""
    
    print(f"\n💡 SOLUTION APPROACHES:")
    print(f"="*30)
    
    print(f"1️⃣ ACCOUNT LINKING (Recommended):")
    print(f"   - Check if email exists before creating Google user")
    print(f"   - Link Google OAuth to existing email/password account")
    print(f"   - User can login with either method → same account")
    
    print(f"\n2️⃣ EMAIL UNIQUENESS ENFORCEMENT:")
    print(f"   - Prevent duplicate emails entirely")
    print(f"   - Show error: 'Email already exists, please login'")
    
    print(f"\n3️⃣ ACCOUNT MERGING:")
    print(f"   - Detect duplicates and merge accounts")
    print(f"   - Combine profile data from both sources")

def main():
    """Main test function"""
    
    try:
        # Test the conflict scenario
        user_count = test_email_conflict_scenario()
        
        # Show solutions
        test_solution_approach()
        
        print(f"\n🎯 CONCLUSION:")
        print(f"="*15)
        
        if user_count > 1:
            print(f"❌ Current setup allows duplicate accounts with same email")
            print(f"🔧 You should implement account linking to fix this")
        else:
            print(f"✅ No conflicts detected in this test")
        
        print(f"\n💡 Next steps:")
        print(f"   1. Decide on your preferred approach")
        print(f"   2. Implement account linking logic")
        print(f"   3. Test with real Google OAuth flow")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
