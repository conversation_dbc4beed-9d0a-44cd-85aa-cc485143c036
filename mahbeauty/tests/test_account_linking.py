#!/usr/bin/env python3
"""
Test account linking functionality
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mahbeauty.settings')
django.setup()

from account.models import UserAccount
from social_django.models import UserS<PERSON>ial<PERSON>uth
from account.social_auth_pipeline import associate_by_email, create_user_with_social_data

def test_account_linking():
    """Test that account linking works correctly"""
    
    print("🧪 Testing Account Linking Functionality")
    print("="*50)
    
    test_email = "<EMAIL>"
    
    # Clean up
    UserAccount.objects.filter(email=test_email).delete()
    print(f"🧹 Cleaned up existing test data for {test_email}")
    
    # Step 1: Create user via <PERSON><PERSON><PERSON> (email/password)
    print(f"\n1️⃣ Creating user via <PERSON><PERSON><PERSON> (email/password)...")
    
    djoser_user = UserAccount.objects.create_user(
        email=test_email,
        password="testpassword123",
        first_name="<PERSON>",
        last_name="EmailUser"
    )
    print(f"   ✅ User created: ID={djoser_user.id}, Name={djoser_user.get_full_name()}")
    
    # Step 2: Simulate Google OAuth login with same email
    print(f"\n2️⃣ Simulating Google OAuth login with same email...")
    
    # Mock backend and details (simulating what social auth provides)
    class MockBackend:
        name = 'google-oauth2'
    
    backend = MockBackend()
    details = {
        'email': test_email,
        'first_name': 'John',
        'last_name': 'GoogleUser'
    }
    kwargs = {
        'uid': '***************'  # Use unique UID for this test
    }
    
    # Test the pipeline function
    result = associate_by_email(
        backend=backend,
        details=details,
        user=None,
        **kwargs
    )
    
    if result and result.get('user'):
        linked_user = result['user']
        is_new = result.get('is_new', False)
        
        print(f"   ✅ Account linking successful!")
        print(f"   📧 Linked to existing user: ID={linked_user.id}")
        print(f"   🆕 Is new user: {is_new}")
        
        # Create the social auth record manually (normally done by pipeline)
        social_auth, created = UserSocialAuth.objects.get_or_create(
            user=linked_user,
            provider='google-oauth2',
            uid=kwargs['uid'],
            defaults={
                'extra_data': {
                    'email': test_email,
                    'name': 'John GoogleUser',
                    'first_name': 'John',
                    'last_name': 'GoogleUser'
                }
            }
        )
        
        if created:
            print(f"   🔗 Social auth record created")
        
    else:
        print(f"   ❌ Account linking failed")
        return False
    
    # Step 3: Verify the results
    print(f"\n3️⃣ Verifying results...")
    
    all_users = UserAccount.objects.filter(email=test_email)
    print(f"   👥 Total users with email {test_email}: {all_users.count()}")
    
    if all_users.count() == 1:
        user = all_users.first()
        print(f"   ✅ Single user account: ID={user.id}, Name={user.get_full_name()}")
        
        # Check social auth
        social_auths = UserSocialAuth.objects.filter(user=user)
        print(f"   🔗 Social auth records: {social_auths.count()}")
        
        for auth in social_auths:
            print(f"     - {auth.provider}: UID={auth.uid}")
        
        # Check login capabilities
        print(f"\n🔐 LOGIN CAPABILITIES:")
        print(f"   📧 Email/password: ✅ (original account)")
        print(f"   🔵 Google OAuth: ✅ (linked account)")
        print(f"   🎯 Result: Same user data regardless of login method!")
        
        return True
    else:
        print(f"   ❌ Expected 1 user, found {all_users.count()}")
        return False

def test_new_user_creation():
    """Test that new users are still created when no existing account exists"""
    
    print(f"\n" + "="*50)
    print("🧪 Testing New User Creation (No Existing Account)")
    print("="*50)
    
    test_email = "<EMAIL>"
    
    # Clean up
    UserAccount.objects.filter(email=test_email).delete()
    print(f"🧹 Cleaned up existing test data for {test_email}")
    
    # Mock backend and details
    class MockBackend:
        name = 'google-oauth2'
    
    backend = MockBackend()
    details = {
        'email': test_email,
        'first_name': 'Jane',
        'last_name': 'NewUser'
    }
    kwargs = {
        'uid': '***************'
    }
    
    # Test associate_by_email (should return None - no existing user)
    result1 = associate_by_email(
        backend=backend,
        details=details,
        user=None,
        **kwargs
    )
    
    if result1 is None:
        print(f"✅ No existing user found - will create new user")
        
        # Test create_user_with_social_data
        result2 = create_user_with_social_data(
            strategy=None,
            details=details,
            backend=backend,
            user=None,
            **kwargs
        )
        
        if result2 and result2.get('user'):
            new_user = result2['user']
            is_new = result2.get('is_new', False)
            
            print(f"✅ New user created: ID={new_user.id}, Name={new_user.get_full_name()}")
            print(f"🆕 Is new user: {is_new}")
            
            return True
        else:
            print(f"❌ Failed to create new user")
            return False
    else:
        print(f"❌ Unexpected result: {result1}")
        return False

def main():
    """Main test function"""
    
    try:
        # Test account linking
        linking_success = test_account_linking()
        
        # Test new user creation
        creation_success = test_new_user_creation()
        
        print(f"\n" + "="*50)
        print("🎯 FINAL RESULTS")
        print("="*50)
        
        print(f"🔗 Account Linking: {'✅ PASSED' if linking_success else '❌ FAILED'}")
        print(f"🆕 New User Creation: {'✅ PASSED' if creation_success else '❌ FAILED'}")
        
        if linking_success and creation_success:
            print(f"\n🎉 All tests PASSED!")
            print(f"💡 Your account linking is working correctly!")
            print(f"\n📋 What this means:")
            print(f"   ✅ Same email + password → Links to existing account")
            print(f"   ✅ New email + Google → Creates new account")
            print(f"   ✅ Users can login with either method → same data")
        else:
            print(f"\n⚠️  Some tests failed - check the implementation")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
