#!/usr/bin/env python3
"""
Create a test order to verify the notification system works automatically
"""

import os
import sys
import django
import random

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mahbeauty.settings')
django.setup()

from orders.models import Order, Payment, PaymentMethod
from account.models import UserAccount


def create_test_order():
    """Create a test order to trigger notifications"""
    print("🛒 Creating test order to verify notification system...")
    print("=" * 60)
    
    # Get a customer user
    customer = UserAccount.objects.filter(is_staff=False).first()
    if not customer:
        print("❌ No customer users found. Please create a customer user first.")
        return
    
    print(f"👤 Using customer: {customer.email}")
    
    # Get a payment method
    payment_method = PaymentMethod.objects.first()
    if not payment_method:
        print("❌ No payment methods found. Please create a payment method first.")
        return
    
    print(f"💳 Using payment method: {payment_method.name}")
    
    # Create a payment
    payment = Payment.objects.create(
        user=customer,
        payment_id=f"PAY-{random.randint(100000, 999999)}",
        payment_method=payment_method
    )
    print(f"💰 Created payment: {payment.payment_id}")
    
    # Create a test order (this should trigger the signals automatically)
    order_number = f"ORD-TEST-{random.randint(100000, 999999)}"
    
    print(f"📦 Creating order: {order_number}")
    print("🔔 This should trigger automatic notifications...")
    print("-" * 60)
    
    order = Order.objects.create(
        user=customer,
        payment=payment,
        order_number=order_number,
        first_name="Test",
        last_name="Customer",
        phone="1234567890",
        email="<EMAIL>",
        state="Test State",
        area="Test Area", 
        address="123 Test Street",
        grand_total=1500,
        tax=150.0,
        order_note="Test order created to verify notification system"
    )
    
    print(f"✅ Successfully created test order: {order.order_number}")
    print(f"👤 Customer: {order.full_name()} ({order.email})")
    print(f"💰 Total: ₹{order.grand_total}")
    print(f"📅 Created: {order.created_at}")
    
    print("\n" + "=" * 60)
    print("🔔 NOTIFICATION SYSTEM TEST RESULTS:")
    print("=" * 60)
    print("If the notification system is working correctly, you should see:")
    print("1. Admin notification emails in the console output above")
    print("2. Customer confirmation emails in the console output above")
    print("3. Log messages about notifications being sent")
    print("\nIf you don't see email output, check:")
    print("- Django signals are properly connected (orders/apps.py)")
    print("- Email backend is configured (should be console for testing)")
    print("- No errors in the Django logs")
    
    return order


if __name__ == "__main__":
    create_test_order()
