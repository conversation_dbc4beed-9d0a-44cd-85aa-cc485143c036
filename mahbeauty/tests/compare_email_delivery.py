#!/usr/bin/env python3
"""
Compare email delivery between admin and customer emails
"""

import os
import sys
import django
import time
from datetime import datetime

# Setup Django first before importing any Django modules
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "mahbeauty.settings")
django.setup()

from django.conf import settings
from django.core.mail import send_mail, EmailMultiAlternatives


def send_identical_test_emails():
    """Send identical emails to both admin and customer"""
    print("📧 SENDING IDENTICAL TEST EMAILS")
    print("=" * 50)
    
    admin_email = "<EMAIL>"
    customer_email = "<EMAIL>"
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Test 1: Simple email with same content
    print("📨 Test 1: Simple Email (same content to both)")
    print("-" * 45)
    
    subject = f"MahBeauty Test Email - {timestamp}"
    message = f"""This is a test email from MahBeauty.

Timestamp: {timestamp}
From: {settings.EMAIL_HOST_USER}
Email Backend: {settings.EMAIL_BACKEND}
Email Host: {settings.EMAIL_HOST}

This email is being sent to test delivery differences between admin and customer emails.

If you receive this email, please confirm!
"""
    
    # Send to admin
    try:
        admin_result = send_mail(
            subject=f"[ADMIN] {subject}",
            message=f"[TO ADMIN]\n\n{message}",
            from_email=settings.EMAIL_HOST_USER,
            recipient_list=[admin_email],
            fail_silently=False,
        )
        print(f"✅ Admin email sent: {admin_result}")
    except Exception as e:
        print(f"❌ Admin email failed: {e}")
    
    # Wait 2 seconds
    time.sleep(2)
    
    # Send to customer
    try:
        customer_result = send_mail(
            subject=f"[CUSTOMER] {subject}",
            message=f"[TO CUSTOMER]\n\n{message}",
            from_email=settings.EMAIL_HOST_USER,
            recipient_list=[customer_email],
            fail_silently=False,
        )
        print(f"✅ Customer email sent: {customer_result}")
    except Exception as e:
        print(f"❌ Customer email failed: {e}")
    
    print()
    
    # Test 2: Using different sender addresses
    print("📨 Test 2: Different Sender Addresses")
    print("-" * 40)
    
    senders = [
        ("EMAIL_HOST_USER", settings.EMAIL_HOST_USER),
        ("DEFAULT_FROM_EMAIL", settings.DEFAULT_FROM_EMAIL),
        ("With Display Name", f"MahBeauty <{settings.EMAIL_HOST_USER}>")
    ]
    
    for sender_name, sender_email in senders:
        print(f"\n🔄 Testing with {sender_name}: {sender_email}")
        
        test_subject = f"MahBeauty {sender_name} Test - {timestamp}"
        test_message = f"""Sender Test: {sender_name}
From: {sender_email}
Timestamp: {timestamp}

This tests different sender configurations.
"""
        
        # Send to admin
        try:
            send_mail(
                subject=f"[ADMIN-{sender_name}] {test_subject}",
                message=f"[TO ADMIN]\n\n{test_message}",
                from_email=sender_email,
                recipient_list=[admin_email],
                fail_silently=False,
            )
            print(f"   ✅ Admin: {sender_name}")
        except Exception as e:
            print(f"   ❌ Admin failed: {e}")
        
        time.sleep(1)
        
        # Send to customer
        try:
            send_mail(
                subject=f"[CUSTOMER-{sender_name}] {test_subject}",
                message=f"[TO CUSTOMER]\n\n{test_message}",
                from_email=sender_email,
                recipient_list=[customer_email],
                fail_silently=False,
            )
            print(f"   ✅ Customer: {sender_name}")
        except Exception as e:
            print(f"   ❌ Customer failed: {e}")
        
        time.sleep(1)


def send_html_comparison():
    """Send HTML emails to compare"""
    print("📨 Test 3: HTML Email Comparison")
    print("-" * 35)
    
    admin_email = "<EMAIL>"
    customer_email = "<EMAIL>"
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    html_content = f"""
    <html>
    <body style="font-family: Arial, sans-serif; margin: 20px;">
        <h2 style="color: #e91e63;">MahBeauty HTML Test</h2>
        <p><strong>Timestamp:</strong> {timestamp}</p>
        <p><strong>From:</strong> {settings.EMAIL_HOST_USER}</p>
        <p><strong>Host:</strong> {settings.EMAIL_HOST}</p>
        
        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>Email Delivery Test</h3>
            <p>This HTML email is testing delivery differences between admin and customer addresses.</p>
            <p style="color: #666;">If you receive this, please confirm the delivery!</p>
        </div>
        
        <p style="font-size: 12px; color: #999;">
            Sent from MahBeauty Django Application
        </p>
    </body>
    </html>
    """
    
    plain_content = f"""MahBeauty HTML Test

Timestamp: {timestamp}
From: {settings.EMAIL_HOST_USER}
Host: {settings.EMAIL_HOST}

Email Delivery Test
This HTML email is testing delivery differences between admin and customer addresses.
If you receive this, please confirm the delivery!

Sent from MahBeauty Django Application
"""
    
    # Send to admin
    try:
        admin_email_obj = EmailMultiAlternatives(
            subject=f"[ADMIN-HTML] MahBeauty HTML Test - {timestamp}",
            body=f"[TO ADMIN]\n\n{plain_content}",
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[admin_email],
        )
        admin_email_obj.attach_alternative(f"[TO ADMIN]<br><br>{html_content}", "text/html")
        admin_email_obj.send()
        print("✅ Admin HTML email sent")
    except Exception as e:
        print(f"❌ Admin HTML email failed: {e}")
    
    time.sleep(2)
    
    # Send to customer
    try:
        customer_email_obj = EmailMultiAlternatives(
            subject=f"[CUSTOMER-HTML] MahBeauty HTML Test - {timestamp}",
            body=f"[TO CUSTOMER]\n\n{plain_content}",
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[customer_email],
        )
        customer_email_obj.attach_alternative(f"[TO CUSTOMER]<br><br>{html_content}", "text/html")
        customer_email_obj.send()
        print("✅ Customer HTML email sent")
    except Exception as e:
        print(f"❌ Customer HTML email failed: {e}")


def main():
    print("🔍 EMAIL DELIVERY COMPARISON TEST")
    print("=" * 50)
    print(f"Admin Email: <EMAIL>")
    print(f"Customer Email: <EMAIL>")
    print(f"SMTP Host: {settings.EMAIL_HOST}")
    print(f"SMTP User: {settings.EMAIL_HOST_USER}")
    print()
    
    # Send identical emails
    send_identical_test_emails()
    print()
    
    # Send HTML comparison
    send_html_comparison()
    print()
    
    print("📊 TEST COMPLETED")
    print("-" * 20)
    print("Multiple test emails have been sent to both addresses.")
    print("Please check both inboxes and compare:")
    print()
    print("Admin Gmail (<EMAIL>):")
    print("- Check inbox, spam, and all mail")
    print("- Look for emails with [ADMIN] prefix")
    print()
    print("Customer Gmail (<EMAIL>):")
    print("- Check inbox, spam, and all mail") 
    print("- Look for emails with [CUSTOMER] prefix")
    print()
    print("🎯 Goal: Determine if there's a delivery difference")
    print("between the two Gmail addresses.")


if __name__ == "__main__":
    main()
