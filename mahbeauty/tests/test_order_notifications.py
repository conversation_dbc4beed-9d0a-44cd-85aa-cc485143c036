#!/usr/bin/env python3
"""
Test script for order notification system
"""

import os
import sys
import django

# Setup Django first before importing any Django modules
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "mahbeauty.settings")
django.setup()

# Now import Django modules after setup
from orders.utils import send_order_notification_email, send_order_confirmation_email
from orders.notifications import order_notification_manager
from orders.models import Order, Payment, PaymentMethod
from django.contrib.auth import get_user_model

User = get_user_model()


def test_notification_system():
    """Test the order notification system"""
    print("🧪 Testing Order Notification System")
    print("=" * 50)

    # Get or create a test order
    order = Order.objects.order_by("-created_at").first()

    if not order:
        print("❌ No orders found in database. Please create an order first.")
        return

    print(f"📦 Testing with order: {order.order_number}")
    print(f"👤 Customer: {order.full_name()} ({order.email})")
    print(f"💰 Total: ₹{order.grand_total}")
    print(f"📅 Created: {order.created_at}")
    print("-" * 50)

    # Test admin notification email
    print("📧 Testing admin notification email...")
    try:
        result = send_order_notification_email(order)
        if result:
            print("✅ Admin notification email sent successfully")
        else:
            print("❌ Failed to send admin notification email")
    except Exception as e:
        print(f"❌ Error sending admin notification: {e}")

    print()

    # Test customer confirmation email
    print("📧 Testing customer confirmation email...")
    try:
        result = send_order_confirmation_email(order)
        if result:
            print("✅ Customer confirmation email sent successfully")
        else:
            print("❌ Failed to send customer confirmation email")
    except Exception as e:
        print(f"❌ Error sending customer confirmation: {e}")

    print()

    # Test notification manager
    print("🔔 Testing notification manager...")
    try:
        notifications_sent = order_notification_manager.notify_order_created(order)
        print(f"✅ Notification manager completed. Sent: {notifications_sent}")
    except Exception as e:
        print(f"❌ Error with notification manager: {e}")

    print()

    # Test status change notification
    print("🔄 Testing status change notification...")
    try:
        old_status = order.status
        new_status = "Shipped" if old_status != "Shipped" else "Delivered"

        notifications_sent = order_notification_manager.notify_order_status_changed(
            order, old_status, new_status
        )
        print(f"✅ Status change notification completed. Sent: {notifications_sent}")
        print(f"   Status change: {old_status} -> {new_status}")
    except Exception as e:
        print(f"❌ Error with status change notification: {e}")

    print()
    print("=" * 50)
    print("🏁 Test completed!")
    print()
    print("📝 Notes:")
    print("- If using console email backend, check console output for email content")
    print("- Configure SMTP settings in environment variables for real email sending")
    print("- Check Django logs for detailed notification information")


def show_email_configuration():
    """Show current email configuration"""
    from django.conf import settings

    print("📧 Current Email Configuration:")
    print("-" * 30)
    print(f"EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    print(f"EMAIL_HOST: {getattr(settings, 'EMAIL_HOST', 'Not set')}")
    print(f"EMAIL_PORT: {getattr(settings, 'EMAIL_PORT', 'Not set')}")
    print(f"EMAIL_USE_TLS: {getattr(settings, 'EMAIL_USE_TLS', 'Not set')}")
    print(f"DEFAULT_FROM_EMAIL: {getattr(settings, 'DEFAULT_FROM_EMAIL', 'Not set')}")
    print(f"SITE_NAME: {getattr(settings, 'SITE_NAME', 'Not set')}")
    print(f"DOMAIN: {getattr(settings, 'DOMAIN', 'Not set')}")
    print()


if __name__ == "__main__":
    show_email_configuration()
    test_notification_system()
