#!/usr/bin/env python3
"""
Test admin navigation for social auth models with real data
"""

import requests
import json
from requests.auth import HTTPBasicAuth

BASE_URL = "http://127.0.0.1:8000"

def test_admin_login():
    """Test admin login and get session"""
    session = requests.Session()
    
    # Get CSRF token
    login_page = session.get(f"{BASE_URL}/admin/login/")
    if login_page.status_code != 200:
        print(f"❌ Cannot access admin login page: {login_page.status_code}")
        return None
    
    print("✅ Admin login page accessible")
    return session

def test_social_auth_urls(session=None):
    """Test social auth admin URLs"""
    
    print("\n🧪 Testing Social Auth Admin Navigation")
    print("="*50)
    
    # URLs to test
    test_urls = [
        {
            'name': '📱 Google & Facebook Logins',
            'url': '/admin/social_django/usersocialauth/',
            'expected_data': '<PERSON>'  # Should show our test user
        },
        {
            'name': '🛡️ Login Security Tokens',
            'url': '/admin/social_django/nonce/',
            'expected_data': 'accounts.google.com'  # Should show Google nonce
        },
        {
            'name': '🤝 Social Login Connections',
            'url': '/admin/social_django/association/',
            'expected_data': 'accounts.google.com'  # Should show Google association
        }
    ]
    
    results = []
    
    for test_case in test_urls:
        print(f"\n🔍 Testing: {test_case['name']}")
        print(f"   URL: {test_case['url']}")
        
        try:
            if session:
                response = session.get(f"{BASE_URL}{test_case['url']}")
            else:
                response = requests.get(f"{BASE_URL}{test_case['url']}")
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ URL accessible")
                
                # Check if expected data is present
                if test_case['expected_data'] in response.text:
                    print(f"   ✅ Contains expected data: {test_case['expected_data']}")
                    results.append({'name': test_case['name'], 'status': 'SUCCESS', 'has_data': True})
                else:
                    print(f"   ⚠️  URL works but no expected data found")
                    results.append({'name': test_case['name'], 'status': 'SUCCESS', 'has_data': False})
                    
            elif response.status_code == 302:
                print("   🔄 Redirected (probably to login) - URL structure is correct")
                results.append({'name': test_case['name'], 'status': 'REDIRECT', 'has_data': False})
                
            elif response.status_code == 404:
                print("   ❌ URL not found - navigation broken")
                results.append({'name': test_case['name'], 'status': 'NOT_FOUND', 'has_data': False})
                
            else:
                print(f"   ❌ Unexpected status: {response.status_code}")
                results.append({'name': test_case['name'], 'status': f'ERROR_{response.status_code}', 'has_data': False})
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append({'name': test_case['name'], 'status': 'EXCEPTION', 'has_data': False})
    
    return results

def test_data_counts():
    """Test that our test data was created successfully"""
    
    print("\n📊 Verifying Test Data")
    print("="*30)
    
    import os
    import sys
    import django
    
    # Setup Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mahbeauty.settings')
    django.setup()
    
    from social_django.models import UserSocialAuth, Nonce, Association
    
    # Count records
    social_users = UserSocialAuth.objects.count()
    nonces = Nonce.objects.count()
    associations = Association.objects.count()
    
    print(f"📱 Google & Facebook Logins: {social_users} records")
    print(f"🛡️ Login Security Tokens: {nonces} records")
    print(f"🤝 Social Login Connections: {associations} records")
    
    # Show sample data
    if social_users > 0:
        sample_user = UserSocialAuth.objects.first()
        print(f"   Sample: {sample_user.user.get_full_name()} via {sample_user.provider}")
    
    if nonces > 0:
        sample_nonce = Nonce.objects.first()
        print(f"   Sample: {sample_nonce.server_url}")
    
    if associations > 0:
        sample_assoc = Association.objects.first()
        print(f"   Sample: {sample_assoc.server_url}")
    
    return {
        'social_users': social_users,
        'nonces': nonces,
        'associations': associations
    }

def display_results(url_results, data_counts):
    """Display final test results"""
    
    print("\n" + "="*60)
    print("🎯 NAVIGATION TEST RESULTS")
    print("="*60)
    
    print(f"\n📊 Test Data Status:")
    print(f"   📱 Google & Facebook Logins: {data_counts['social_users']} records")
    print(f"   🛡️ Login Security Tokens: {data_counts['nonces']} records")
    print(f"   🤝 Social Login Connections: {data_counts['associations']} records")
    
    print(f"\n🔗 URL Navigation Status:")
    
    success_count = 0
    total_count = len(url_results)
    
    for result in url_results:
        status_icon = {
            'SUCCESS': '✅',
            'REDIRECT': '🔄',
            'NOT_FOUND': '❌',
        }.get(result['status'], '❌')
        
        data_icon = '📊' if result['has_data'] else '📭'
        
        print(f"   {status_icon} {result['name']}: {result['status']} {data_icon}")
        
        if result['status'] in ['SUCCESS', 'REDIRECT']:
            success_count += 1
    
    print(f"\n📈 Overall Results:")
    print(f"   ✅ Working URLs: {success_count}/{total_count}")
    print(f"   📊 Total test records: {sum(data_counts.values())}")
    
    if success_count == total_count and sum(data_counts.values()) > 0:
        print(f"\n🎉 All navigation tests PASSED!")
        print(f"💡 Your social auth admin navigation is working perfectly!")
    else:
        print(f"\n⚠️  Some issues found. Check the details above.")

def main():
    """Main test function"""
    
    print("🧪 Testing Social Auth Admin Navigation with Data")
    print("="*60)
    
    # Test data counts
    data_counts = test_data_counts()
    
    # Test admin session (optional)
    session = test_admin_login()
    
    # Test URLs
    url_results = test_social_auth_urls(session)
    
    # Display results
    display_results(url_results, data_counts)
    
    print(f"\n💡 To manually test:")
    print(f"   1. Start server: python manage.py runserver")
    print(f"   2. Go to: http://127.0.0.1:8000/admin/")
    print(f"   3. Login and check the 'Social Django' section")

if __name__ == "__main__":
    main()
