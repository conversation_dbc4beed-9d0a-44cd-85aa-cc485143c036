#!/usr/bin/env python3
"""
Test script to check customer email notifications
"""

import os
import sys
import django

# Setup Django first before importing any Django modules
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "mahbeauty.settings")
django.setup()

from django.conf import settings
from django.core.mail import send_mail
from orders.utils import send_order_confirmation_email
from orders.models import Order
from orders.notifications import order_notification_manager


def test_customer_email_direct():
    """Test sending email directly to customer email"""
    print("📧 TESTING DIRECT EMAIL TO CUSTOMER")
    print("-" * 40)
    
    customer_email = "<EMAIL>"
    
    try:
        result = send_mail(
            subject="Test Email from MahBeauty",
            message="This is a test email to verify customer email delivery.\n\n"
                   f"Sent from: {settings.EMAIL_HOST_USER}\n"
                   f"Email backend: {settings.EMAIL_BACKEND}\n"
                   f"Email host: {settings.EMAIL_HOST}\n\n"
                   "If you receive this, customer emails are working!",
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[customer_email],
            fail_silently=False,
        )
        print(f"✅ Direct customer email sent successfully to {customer_email}")
        print(f"   Result: {result}")
        return True
    except Exception as e:
        print(f"❌ Failed to send direct customer email: {e}")
        return False


def test_order_confirmation_email():
    """Test order confirmation email with existing order"""
    print("📦 TESTING ORDER CONFIRMATION EMAIL")
    print("-" * 40)
    
    # Get the latest order
    order = Order.objects.order_by("-created_at").first()
    
    if not order:
        print("❌ No orders found in database!")
        return False
    
    print(f"Using order: {order.order_number}")
    print(f"Original customer: {order.full_name()} ({order.email})")
    
    # Temporarily change the order email to test customer
    original_email = order.email
    order.email = "<EMAIL>"
    order.save()
    
    print(f"Changed customer email to: {order.email}")
    
    try:
        result = send_order_confirmation_email(order)
        if result:
            print("✅ Order confirmation email sent successfully")
        else:
            print("❌ Order confirmation email failed")
        
        # Restore original email
        order.email = original_email
        order.save()
        print(f"Restored original email: {original_email}")
        
        return result
    except Exception as e:
        # Restore original email even if there's an error
        order.email = original_email
        order.save()
        print(f"❌ Error sending order confirmation: {e}")
        return False


def test_full_notification_system():
    """Test the complete notification system"""
    print("🔔 TESTING COMPLETE NOTIFICATION SYSTEM")
    print("-" * 40)
    
    # Get the latest order
    order = Order.objects.order_by("-created_at").first()
    
    if not order:
        print("❌ No orders found in database!")
        return False
    
    print(f"Using order: {order.order_number}")
    print(f"Original customer: {order.full_name()} ({order.email})")
    
    # Temporarily change the order email to test customer
    original_email = order.email
    order.email = "<EMAIL>"
    order.save()
    
    print(f"Changed customer email to: {order.email}")
    
    try:
        notifications_sent = order_notification_manager.notify_order_created(order)
        print(f"✅ Notification manager completed")
        print(f"   Notifications sent: {notifications_sent}")
        
        # Restore original email
        order.email = original_email
        order.save()
        print(f"Restored original email: {original_email}")
        
        return len(notifications_sent) > 0
    except Exception as e:
        # Restore original email even if there's an error
        order.email = original_email
        order.save()
        print(f"❌ Error with notification system: {e}")
        return False


def show_email_templates():
    """Show what email templates will be used"""
    print("📄 EMAIL TEMPLATES BEING USED")
    print("-" * 35)
    
    print("Customer confirmation templates:")
    print("- orders/emails/customer_order_confirmation.html")
    print("- orders/emails/customer_order_confirmation.txt")
    print()
    
    print("Admin notification templates:")
    print("- orders/emails/admin_order_notification.html") 
    print("- orders/emails/admin_order_notification.txt")
    print()


def main():
    print("🧪 CUSTOMER EMAIL TEST")
    print("=" * 50)
    
    print(f"📧 Target customer email: <EMAIL>")
    print(f"📧 Admin email: {settings.ADMINS[0][1] if settings.ADMINS else 'Not set'}")
    print(f"📧 From email: {settings.DEFAULT_FROM_EMAIL}")
    print(f"📧 SMTP User: {settings.EMAIL_HOST_USER}")
    print()
    
    # Show templates
    show_email_templates()
    
    # Test direct customer email
    direct_result = test_customer_email_direct()
    print()
    
    # Test order confirmation
    confirmation_result = test_order_confirmation_email()
    print()
    
    # Test full notification system
    full_system_result = test_full_notification_system()
    print()
    
    # Summary
    print("📊 TEST SUMMARY")
    print("-" * 20)
    print(f"Direct customer email: {'✅ PASS' if direct_result else '❌ FAIL'}")
    print(f"Order confirmation: {'✅ PASS' if confirmation_result else '❌ FAIL'}")
    print(f"Full notification system: {'✅ PASS' if full_system_result else '❌ FAIL'}")
    
    if all([direct_result, confirmation_result, full_system_result]):
        print("\n🎉 ALL CUSTOMER EMAIL TESTS PASSED!")
        print("Customer should receive emails at: <EMAIL>")
        print("\nCheck the following:")
        print("1. Inbox for <NAME_EMAIL>")
        print("2. Spam folder")
        print("3. All Mail folder")
    else:
        print("\n🚨 SOME CUSTOMER EMAIL TESTS FAILED!")
        print("Check the error messages above.")


if __name__ == "__main__":
    main()
