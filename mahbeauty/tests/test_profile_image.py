#!/usr/bin/env python
"""
Test script to verify profile image functionality
"""

import os
import sys
import django
from io import BytesIO
from PIL import Image

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mahbeauty.settings')
django.setup()

from django.core.files.uploadedfile import SimpleUploadedFile
from account.models import UserAccount
from account.utils import (
    download_image_from_url, 
    resize_profile_image, 
    save_profile_image_from_url,
    get_social_profile_image_url
)


def create_test_image():
    """Create a test image for testing"""
    # Create a simple test image
    img = Image.new('RGB', (500, 500), color='red')
    img_io = BytesIO()
    img.save(img_io, format='JPEG')
    img_io.seek(0)
    
    return SimpleUploadedFile(
        name='test_profile.jpg',
        content=img_io.getvalue(),
        content_type='image/jpeg'
    )


def test_user_profile_image():
    """Test basic profile image functionality"""
    print("🧪 Testing User Profile Image Functionality")
    print("=" * 50)
    
    # Create a test user
    test_email = "<EMAIL>"
    
    # Clean up any existing test user
    UserAccount.objects.filter(email=test_email).delete()
    
    user = UserAccount.objects.create_user(
        email=test_email,
        first_name="Test",
        last_name="User",
        password="testpass123"
    )
    
    print(f"✅ Created test user: {user.email}")
    
    # Test 1: Check initial state
    print(f"📷 Has profile image: {user.has_profile_image()}")
    print(f"🔗 Profile image URL: {user.get_profile_image_url()}")
    
    # Test 2: Add profile image
    test_image = create_test_image()
    user.profile_image.save('test_profile.jpg', test_image, save=True)
    
    print(f"✅ Added profile image")
    print(f"📷 Has profile image: {user.has_profile_image()}")
    print(f"🔗 Profile image URL: {user.get_profile_image_url()}")
    
    # Test 3: Test utility functions
    print("\n🔧 Testing utility functions:")
    
    # Test image resizing
    test_image_2 = create_test_image()
    resized = resize_profile_image(test_image_2, max_width=100, max_height=100)
    print(f"✅ Image resizing works")
    
    # Test social profile image URL extraction
    google_extra_data = {'picture': 'https://example.com/profile.jpg'}
    facebook_extra_data = {'picture': {'data': {'url': 'https://example.com/fb_profile.jpg'}}}
    
    google_url = get_social_profile_image_url('google-oauth2', google_extra_data)
    facebook_url = get_social_profile_image_url('facebook', facebook_extra_data)
    
    print(f"✅ Google profile URL extraction: {google_url}")
    print(f"✅ Facebook profile URL extraction: {facebook_url}")
    
    # Clean up
    user.delete()
    print(f"\n🧹 Cleaned up test user")
    
    print("\n🎉 All profile image tests passed!")


def test_serializer_fields():
    """Test that serializers include profile image fields"""
    print("\n🧪 Testing Serializer Fields")
    print("=" * 50)
    
    from account.serializers import UserCreateSerializer, UserProfileSerializer
    
    # Test UserCreateSerializer
    create_fields = UserCreateSerializer.Meta.fields
    print(f"📝 UserCreateSerializer fields: {create_fields}")
    assert 'profile_image' in create_fields, "profile_image not in UserCreateSerializer fields"
    print("✅ UserCreateSerializer includes profile_image")
    
    # Test UserProfileSerializer
    profile_fields = UserProfileSerializer.Meta.fields
    print(f"📝 UserProfileSerializer fields: {profile_fields}")
    assert 'profile_image' in profile_fields, "profile_image not in UserProfileSerializer fields"
    assert 'profile_image_url' in profile_fields, "profile_image_url not in UserProfileSerializer fields"
    assert 'has_profile_image' in profile_fields, "has_profile_image not in UserProfileSerializer fields"
    print("✅ UserProfileSerializer includes all profile image fields")


def main():
    """Run all tests"""
    print("🚀 Starting Profile Image Tests")
    print("=" * 60)
    
    try:
        test_user_profile_image()
        test_serializer_fields()
        
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED! Profile image functionality is working correctly.")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
