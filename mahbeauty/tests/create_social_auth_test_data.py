#!/usr/bin/env python3
"""
Create test data for social authentication models to test admin navigation
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mahbeauty.settings')
django.setup()

from account.models import UserAccount
from social_django.models import UserSocialAuth, Nonce, Association
from django.utils import timezone
import random

def create_test_users_with_social_auth():
    """Create test users with Google and Facebook social auth"""
    
    print("🧪 Creating test users with social authentication...")
    
    # Test users data
    test_users = [
        {
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>',
            'provider': 'google-oauth2',
            'uid': '***************'
        },
        {
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>',
            'provider': 'facebook',
            'uid': '***************'
        },
        {
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>',
            'provider': 'google-oauth2',
            'uid': '***************'
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Emma',
            'last_name': 'Davis',
            'provider': 'facebook',
            'uid': '***************'
        }
    ]
    
    created_users = []
    
    for user_data in test_users:
        # Create or get user
        user, created = UserAccount.objects.get_or_create(
            email=user_data['email'],
            defaults={
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name'],
                'is_active': True,
            }
        )
        
        if created:
            print(f"✅ Created user: {user.get_full_name()} ({user.email})")
        else:
            print(f"📝 User already exists: {user.get_full_name()} ({user.email})")
        
        # Create social auth entry
        social_auth, created = UserSocialAuth.objects.get_or_create(
            user=user,
            provider=user_data['provider'],
            uid=user_data['uid'],
            defaults={
                'extra_data': {
                    'id': user_data['uid'],
                    'email': user_data['email'],
                    'name': f"{user_data['first_name']} {user_data['last_name']}",
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'verified_email': True,
                    'locale': 'en'
                }
            }
        )
        
        if created:
            print(f"  ✅ Created {user_data['provider']} social auth for {user.email}")
        else:
            print(f"  📝 Social auth already exists for {user.email}")
        
        created_users.append(user)
    
    return created_users

def create_test_nonces():
    """Create test nonces (security tokens)"""
    
    print("\n🔐 Creating test security tokens (nonces)...")
    
    test_nonces = [
        {
            'server_url': 'https://accounts.google.com',
            'timestamp': timezone.now().timestamp(),
            'salt': 'abc123def456'
        },
        {
            'server_url': 'https://www.facebook.com',
            'timestamp': timezone.now().timestamp(),
            'salt': 'xyz789uvw012'
        },
        {
            'server_url': 'https://accounts.google.com',
            'timestamp': timezone.now().timestamp(),
            'salt': 'mno345pqr678'
        }
    ]
    
    for nonce_data in test_nonces:
        nonce, created = Nonce.objects.get_or_create(
            server_url=nonce_data['server_url'],
            timestamp=int(nonce_data['timestamp']),
            salt=nonce_data['salt']
        )
        
        if created:
            print(f"✅ Created nonce for {nonce_data['server_url']}")
        else:
            print(f"📝 Nonce already exists for {nonce_data['server_url']}")

def create_test_associations():
    """Create test associations (social login connections)"""
    
    print("\n🤝 Creating test social login connections (associations)...")
    
    test_associations = [
        {
            'server_url': 'https://accounts.google.com',
            'handle': 'google_oauth2_handle_123',
            'secret': 'secret_key_google_456',
            'issued': int(timezone.now().timestamp()),
            'lifetime': 3600,  # 1 hour
            'assoc_type': 'HMAC-SHA256'
        },
        {
            'server_url': 'https://www.facebook.com',
            'handle': 'facebook_oauth2_handle_789',
            'secret': 'secret_key_facebook_012',
            'issued': int(timezone.now().timestamp()),
            'lifetime': 7200,  # 2 hours
            'assoc_type': 'HMAC-SHA256'
        }
    ]
    
    for assoc_data in test_associations:
        association, created = Association.objects.get_or_create(
            server_url=assoc_data['server_url'],
            handle=assoc_data['handle'],
            defaults={
                'secret': assoc_data['secret'].encode(),
                'issued': assoc_data['issued'],
                'lifetime': assoc_data['lifetime'],
                'assoc_type': assoc_data['assoc_type']
            }
        )
        
        if created:
            print(f"✅ Created association for {assoc_data['server_url']}")
        else:
            print(f"📝 Association already exists for {assoc_data['server_url']}")

def display_summary():
    """Display summary of created test data"""
    
    print("\n" + "="*60)
    print("📊 TEST DATA SUMMARY")
    print("="*60)
    
    # Users with social auth
    social_users = UserSocialAuth.objects.all()
    print(f"\n👥 Google & Facebook Logins: {social_users.count()} entries")
    for social_user in social_users:
        provider_icon = "🔵" if social_user.provider == "google-oauth2" else "🔷"
        print(f"  {provider_icon} {social_user.user.get_full_name()} via {social_user.provider}")
    
    # Nonces
    nonces = Nonce.objects.all()
    print(f"\n🔐 Login Security Tokens: {nonces.count()} entries")
    for nonce in nonces:
        print(f"  🛡️ {nonce.server_url} (salt: {nonce.salt[:10]}...)")
    
    # Associations
    associations = Association.objects.all()
    print(f"\n🤝 Social Login Connections: {associations.count()} entries")
    for association in associations:
        print(f"  🔗 {association.server_url} (handle: {association.handle[:20]}...)")
    
    print(f"\n✅ Total test data entries: {social_users.count() + nonces.count() + associations.count()}")
    print("\n🎯 You can now test the admin navigation at:")
    print("   📱 Google & Facebook Logins: /admin/social_django/usersocialauth/")
    print("   🛡️ Login Security Tokens: /admin/social_django/nonce/")
    print("   🤝 Social Login Connections: /admin/social_django/association/")

def main():
    """Main function to create all test data"""
    
    print("🧪 Creating Social Authentication Test Data")
    print("="*60)
    
    try:
        # Create test data
        users = create_test_users_with_social_auth()
        create_test_nonces()
        create_test_associations()
        
        # Display summary
        display_summary()
        
        print("\n🎉 Test data creation completed successfully!")
        print("💡 Now you can test the admin navigation with real data.")
        
    except Exception as e:
        print(f"\n❌ Error creating test data: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
