#!/usr/bin/env python3
"""
Test Facebook account linking functionality
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mahbeauty.settings')
django.setup()

from account.models import UserAccount
from social_django.models import UserS<PERSON>ialAuth
from account.social_auth_pipeline import associate_by_email, create_user_with_social_data

def test_facebook_account_linking():
    """Test that Facebook account linking works correctly"""
    
    print("🧪 Testing Facebook Account Linking")
    print("="*40)
    
    test_email = "<EMAIL>"
    
    # Clean up
    UserAccount.objects.filter(email=test_email).delete()
    print(f"🧹 Cleaned up existing test data for {test_email}")
    
    # Step 1: Create user via <PERSON><PERSON><PERSON> (email/password)
    print(f"\n1️⃣ Creating user via <PERSON><PERSON><PERSON> (email/password)...")
    
    djoser_user = UserAccount.objects.create_user(
        email=test_email,
        password="testpassword123",
        first_name="<PERSON>",
        last_name="EmailUser"
    )
    print(f"   ✅ User created: ID={djoser_user.id}, Name={djoser_user.get_full_name()}")
    
    # Step 2: Simulate Facebook OAuth login with same email
    print(f"\n2️⃣ Simulating Facebook OAuth login with same email...")
    
    # Mock backend and details (simulating what Facebook provides)
    class MockFacebookBackend:
        name = 'facebook'
    
    backend = MockFacebookBackend()
    details = {
        'email': test_email,
        'first_name': 'Sarah',
        'last_name': 'FacebookUser'
    }
    kwargs = {
        'uid': 'facebook_123456789'  # Facebook UID format
    }
    
    # Test the pipeline function
    result = associate_by_email(
        backend=backend,
        details=details,
        user=None,
        **kwargs
    )
    
    if result and result.get('user'):
        linked_user = result['user']
        is_new = result.get('is_new', False)
        
        print(f"   ✅ Facebook account linking successful!")
        print(f"   📧 Linked to existing user: ID={linked_user.id}")
        print(f"   🆕 Is new user: {is_new}")
        
        # Create the social auth record
        social_auth, created = UserSocialAuth.objects.get_or_create(
            user=linked_user,
            provider='facebook',
            uid=kwargs['uid'],
            defaults={
                'extra_data': {
                    'email': test_email,
                    'name': 'Sarah FacebookUser',
                    'first_name': 'Sarah',
                    'last_name': 'FacebookUser'
                }
            }
        )
        
        if created:
            print(f"   🔗 Facebook social auth record created")
        
    else:
        print(f"   ❌ Facebook account linking failed")
        return False
    
    # Step 3: Verify the results
    print(f"\n3️⃣ Verifying results...")
    
    all_users = UserAccount.objects.filter(email=test_email)
    print(f"   👥 Total users with email {test_email}: {all_users.count()}")
    
    if all_users.count() == 1:
        user = all_users.first()
        print(f"   ✅ Single user account: ID={user.id}, Name={user.get_full_name()}")
        
        # Check social auth
        social_auths = UserSocialAuth.objects.filter(user=user)
        print(f"   🔗 Social auth records: {social_auths.count()}")
        
        for auth in social_auths:
            provider_icon = "🔷" if auth.provider == "facebook" else "🔵"
            print(f"     {provider_icon} {auth.provider}: UID={auth.uid}")
        
        # Check login capabilities
        print(f"\n🔐 LOGIN CAPABILITIES:")
        print(f"   📧 Email/password: ✅ (original account)")
        print(f"   🔷 Facebook OAuth: ✅ (linked account)")
        print(f"   🎯 Result: Same user data regardless of login method!")
        
        return True
    else:
        print(f"   ❌ Expected 1 user, found {all_users.count()}")
        return False

def test_multi_social_linking():
    """Test linking both Google AND Facebook to same email account"""
    
    print(f"\n" + "="*50)
    print("🧪 Testing Multi-Social Account Linking")
    print("="*50)
    
    test_email = "<EMAIL>"
    
    # Clean up
    UserAccount.objects.filter(email=test_email).delete()
    print(f"🧹 Cleaned up existing test data for {test_email}")
    
    # Step 1: Create user via email/password
    print(f"\n1️⃣ Creating user via email/password...")
    
    user = UserAccount.objects.create_user(
        email=test_email,
        password="testpassword123",
        first_name="Alex",
        last_name="MultiUser"
    )
    print(f"   ✅ User created: {user.get_full_name()}")
    
    # Step 2: Link Google account
    print(f"\n2️⃣ Linking Google account...")
    
    google_social = UserSocialAuth.objects.create(
        user=user,
        provider='google-oauth2',
        uid='google_999888777',
        extra_data={'email': test_email, 'name': 'Alex MultiUser'}
    )
    print(f"   🔵 Google linked: UID={google_social.uid}")
    
    # Step 3: Link Facebook account
    print(f"\n3️⃣ Linking Facebook account...")
    
    facebook_social = UserSocialAuth.objects.create(
        user=user,
        provider='facebook',
        uid='facebook_777888999',
        extra_data={'email': test_email, 'name': 'Alex MultiUser'}
    )
    print(f"   🔷 Facebook linked: UID={facebook_social.uid}")
    
    # Step 4: Verify multi-social setup
    print(f"\n4️⃣ Verifying multi-social setup...")
    
    social_auths = UserSocialAuth.objects.filter(user=user)
    print(f"   🔗 Total social auth records: {social_auths.count()}")
    
    for auth in social_auths:
        provider_icon = "🔷" if auth.provider == "facebook" else "🔵"
        print(f"     {provider_icon} {auth.provider}: UID={auth.uid}")
    
    print(f"\n🎯 LOGIN OPTIONS FOR SAME USER:")
    print(f"   📧 Email/password: ✅")
    print(f"   🔵 Google OAuth: ✅") 
    print(f"   🔷 Facebook OAuth: ✅")
    print(f"   🎉 All three methods → SAME account data!")
    
    return social_auths.count() == 2

def test_facebook_api_endpoints():
    """Test Facebook OAuth API endpoints"""
    
    print(f"\n" + "="*50)
    print("🧪 Testing Facebook API Endpoints")
    print("="*50)
    
    import requests
    
    base_url = "http://127.0.0.1:8000/api"
    
    endpoints = [
        {
            'name': 'Facebook OAuth',
            'url': f'{base_url}/auth/o/facebook/',
            'method': 'POST'
        },
        {
            'name': 'Social Auth URLs',
            'url': f'{base_url}/auth/',
            'method': 'GET'
        }
    ]
    
    print("📡 Available Facebook endpoints:")
    
    for endpoint in endpoints:
        try:
            if endpoint['method'] == 'GET':
                response = requests.get(endpoint['url'])
            else:
                # Just check if endpoint exists (will return 400 without data)
                response = requests.post(endpoint['url'], json={})
            
            if response.status_code in [200, 400, 401]:
                print(f"   ✅ {endpoint['name']}: {endpoint['url']} (Status: {response.status_code})")
            else:
                print(f"   ❌ {endpoint['name']}: {endpoint['url']} (Status: {response.status_code})")
                
        except Exception as e:
            print(f"   ⚠️  {endpoint['name']}: Cannot test (server not running)")

def main():
    """Main test function"""
    
    try:
        # Test Facebook account linking
        facebook_success = test_facebook_account_linking()
        
        # Test multi-social linking
        multi_success = test_multi_social_linking()
        
        # Test API endpoints
        test_facebook_api_endpoints()
        
        print(f"\n" + "="*50)
        print("🎯 FACEBOOK LINKING RESULTS")
        print("="*50)
        
        print(f"🔷 Facebook Linking: {'✅ PASSED' if facebook_success else '❌ FAILED'}")
        print(f"🔗 Multi-Social Linking: {'✅ PASSED' if multi_success else '❌ FAILED'}")
        
        if facebook_success and multi_success:
            print(f"\n🎉 Facebook account linking is working perfectly!")
            print(f"\n📋 What this means:")
            print(f"   ✅ Email/password + Facebook → Links to same account")
            print(f"   ✅ Google + Facebook + Email → All link to same account")
            print(f"   ✅ Users can login with any method → same data")
            print(f"   ✅ Facebook OAuth endpoint is available")
        else:
            print(f"\n⚠️  Some Facebook tests failed - check the implementation")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
