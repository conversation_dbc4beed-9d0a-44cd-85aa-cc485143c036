#!/usr/bin/env python3
"""
Debug script to test email configuration and sending
"""

import os
import sys
import django

# Setup Django first before importing any Django modules
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "mahbeauty.settings")
django.setup()

from django.conf import settings
from django.core.mail import send_mail, EmailMultiAlternatives
from orders.utils import get_admin_emails, send_order_notification_email
from orders.models import Order


def check_email_configuration():
    """Check current email configuration"""
    print("🔧 EMAIL CONFIGURATION")
    print("=" * 50)
    print(f"EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    print(f"EMAIL_HOST: {settings.EMAIL_HOST}")
    print(f"EMAIL_PORT: {settings.EMAIL_PORT}")
    print(f"EMAIL_USE_TLS: {settings.EMAIL_USE_TLS}")
    print(f"EMAIL_HOST_USER: {settings.EMAIL_HOST_USER}")
    print(f"EMAIL_HOST_PASSWORD: {'*' * len(settings.EMAIL_HOST_PASSWORD) if settings.EMAIL_HOST_PASSWORD else 'Not set'}")
    print(f"DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")
    print()
    
    print("👥 ADMIN CONFIGURATION")
    print("-" * 30)
    print(f"ADMINS setting: {settings.ADMINS}")
    admin_emails = get_admin_emails()
    print(f"Admin emails from get_admin_emails(): {admin_emails}")
    print()


def test_simple_email():
    """Test sending a simple email"""
    print("📧 TESTING SIMPLE EMAIL")
    print("-" * 30)
    
    admin_emails = get_admin_emails()
    if not admin_emails:
        print("❌ No admin emails found!")
        return False
    
    try:
        result = send_mail(
            subject="Test Email from MahBeauty",
            message="This is a test email to verify email configuration.",
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=admin_emails,
            fail_silently=False,
        )
        print(f"✅ Simple email sent successfully. Result: {result}")
        return True
    except Exception as e:
        print(f"❌ Failed to send simple email: {e}")
        return False


def test_html_email():
    """Test sending HTML email"""
    print("📧 TESTING HTML EMAIL")
    print("-" * 30)
    
    admin_emails = get_admin_emails()
    if not admin_emails:
        print("❌ No admin emails found!")
        return False
    
    try:
        html_content = """
        <html>
        <body>
            <h2>Test HTML Email</h2>
            <p>This is a test HTML email from MahBeauty.</p>
            <p><strong>Email configuration is working!</strong></p>
        </body>
        </html>
        """
        
        email = EmailMultiAlternatives(
            subject="Test HTML Email from MahBeauty",
            body="This is the plain text version.",
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=admin_emails,
        )
        email.attach_alternative(html_content, "text/html")
        
        result = email.send()
        print(f"✅ HTML email sent successfully. Result: {result}")
        return True
    except Exception as e:
        print(f"❌ Failed to send HTML email: {e}")
        return False


def test_order_notification():
    """Test order notification email with a real order"""
    print("📦 TESTING ORDER NOTIFICATION")
    print("-" * 30)
    
    # Get the latest order
    order = Order.objects.order_by("-created_at").first()
    
    if not order:
        print("❌ No orders found in database!")
        return False
    
    print(f"Using order: {order.order_number}")
    print(f"Customer: {order.full_name()} ({order.email})")
    print(f"Total: ₹{order.grand_total}")
    
    try:
        result = send_order_notification_email(order)
        if result:
            print("✅ Order notification email sent successfully")
        else:
            print("❌ Order notification email failed")
        return result
    except Exception as e:
        print(f"❌ Error sending order notification: {e}")
        return False


def main():
    print("🧪 EMAIL DEBUG SCRIPT")
    print("=" * 50)
    
    # Check configuration
    check_email_configuration()
    
    # Test simple email
    simple_result = test_simple_email()
    print()
    
    # Test HTML email
    html_result = test_html_email()
    print()
    
    # Test order notification
    order_result = test_order_notification()
    print()
    
    # Summary
    print("📊 SUMMARY")
    print("-" * 20)
    print(f"Simple email: {'✅ PASS' if simple_result else '❌ FAIL'}")
    print(f"HTML email: {'✅ PASS' if html_result else '❌ FAIL'}")
    print(f"Order notification: {'✅ PASS' if order_result else '❌ FAIL'}")
    
    if not any([simple_result, html_result, order_result]):
        print("\n🚨 ALL TESTS FAILED!")
        print("Check your email configuration and environment variables.")
    elif all([simple_result, html_result, order_result]):
        print("\n🎉 ALL TESTS PASSED!")
        print("Email system is working correctly.")
    else:
        print("\n⚠️ SOME TESTS FAILED!")
        print("Check the specific error messages above.")


if __name__ == "__main__":
    main()
