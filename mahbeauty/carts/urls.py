from django.urls import include, path
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.reverse import reverse
from rest_framework.routers import DefaultRouter

from carts import views

# DRF Router
router = DefaultRouter()
router.register(r"carts", views.CartViewSet, basename="cart")
router.register(r"cart-items", views.CartItemViewSet, basename="cartitem")


@api_view(["GET"])
def carts_api_root(request, format=None):
    """
    API root for carts app
    """
    return Response(
        {
            "carts": reverse("cart-list", request=request, format=format),
            "cart-items": reverse("cartitem-list", request=request, format=format),
        }
    )


urlpatterns = [
    path("", carts_api_root, name="carts-api-root"),
    path("", include(router.urls)),
]
