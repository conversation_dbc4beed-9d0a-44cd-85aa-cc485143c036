# Carts App API Documentation

This document describes the REST API endpoints for the carts app, including serializers and viewsets for managing shopping carts and cart items.

## 📋 Overview

The carts app provides the following API endpoints:
- **Carts**: Shopping cart management
- **Cart Items**: Individual items within shopping carts

## 🔗 Base URL

All endpoints are prefixed with `/api/carts/`

## 📊 Models & Endpoints

### 1. Cart API

**Base URL**: `/api/carts/api/carts/`

#### Model Fields
- `id`: Auto-generated primary key
- `cart_id`: Unique cart identifier (string, max 250 chars)
- `date_added`: Timestamp when cart was created

#### Endpoints

| Method | Endpoint | Description | Permission |
|--------|----------|-------------|------------|
| GET | `/api/carts/api/carts/` | List all carts | Authenticated or ReadOnly |
| POST | `/api/carts/api/carts/` | Create new cart | Authenticated or ReadOnly |
| GET | `/api/carts/api/carts/{id}/` | Retrieve specific cart | Authenticated or ReadOnly |
| PUT | `/api/carts/api/carts/{id}/` | Update cart | Authenticated or ReadOnly |
| PATCH | `/api/carts/api/carts/{id}/` | Partial update cart | Authenticated or ReadOnly |
| DELETE | `/api/carts/api/carts/{id}/` | Delete cart | Authenticated or ReadOnly |

#### Custom Actions

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/carts/api/carts/{id}/items/` | Get all items in cart |
| GET | `/api/carts/api/carts/{id}/total/` | Get cart total and summary |

#### Serializers

**CartSerializer** (Detail view)
```json
{
    "id": 1,
    "cart_id": "uuid-string",
    "date_added": "2024-01-01T12:00:00Z",
    "items_count": 3,
    "total_amount": "150.00",
    "total_quantity": 5
}
```

**CartListSerializer** (List view)
```json
{
    "id": 1,
    "cart_id": "uuid-string", 
    "date_added": "2024-01-01T12:00:00Z",
    "items_count": 3,
    "total_amount": "150.00"
}
```

**CartCreateSerializer** (Create)
```json
{
    "cart_id": "unique-cart-id"
}
```

### 2. Cart Items API

**Base URL**: `/api/carts/api/cart-items/`

#### Model Fields
- `id`: Auto-generated primary key
- `user`: Foreign key to UserAccount (nullable)
- `product`: Foreign key to Product
- `variation`: Many-to-many relationship with Variation
- `cart`: Foreign key to Cart (nullable)
- `quantity`: Integer quantity
- `is_active`: Boolean status

#### Endpoints

| Method | Endpoint | Description | Permission |
|--------|----------|-------------|------------|
| GET | `/api/carts/api/cart-items/` | List cart items | Authenticated or ReadOnly |
| POST | `/api/carts/api/cart-items/` | Add item to cart | Authenticated or ReadOnly |
| GET | `/api/carts/api/cart-items/{id}/` | Retrieve specific item | Authenticated or ReadOnly |
| PUT | `/api/carts/api/cart-items/{id}/` | Update cart item | Authenticated or ReadOnly |
| PATCH | `/api/carts/api/cart-items/{id}/` | Partial update item | Authenticated or ReadOnly |
| DELETE | `/api/carts/api/cart-items/{id}/` | Remove item from cart | Authenticated or ReadOnly |

#### Custom Actions

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/carts/api/cart-items/{id}/update_quantity/` | Update item quantity |
| POST | `/api/carts/api/cart-items/{id}/toggle_active/` | Toggle item active status |

#### Serializers

**CartItemSerializer** (Detail view)
```json
{
    "id": 1,
    "user": {
        "id": 1,
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "full_name": "John Doe"
    },
    "product": {
        "id": 1,
        "name": "Product Name",
        "price": "50.00",
        "image": "/media/products/image.jpg",
        "category_name": "Beauty"
    },
    "variation": [
        {
            "id": 1,
            "variation_category": "color",
            "variation_value": "Red",
            "is_active": true
        }
    ],
    "cart_id": "uuid-string",
    "quantity": 2,
    "is_active": true,
    "sub_total": "100.00"
}
```

**CartItemListSerializer** (List view)
```json
{
    "id": 1,
    "product_name": "Product Name",
    "product_price": "50.00",
    "product_image": "/media/products/image.jpg",
    "user_name": "John Doe",
    "cart_id": "uuid-string",
    "quantity": 2,
    "sub_total": "100.00",
    "variations_display": ["color: Red"],
    "is_active": true
}
```

**CartItemCreateSerializer** (Create)
```json
{
    "product_id": 1,
    "variation_ids": [1, 2],
    "quantity": 2
}
```

## 🔍 Filtering & Search

### Cart Items Filtering
- **Search**: `product__name`, `user__email`, `user__first_name`, `user__last_name`
- **Filters**: `is_active`, `product__category`, `user`
- **Ordering**: `quantity`, `cart__date_added`

### Carts Filtering
- **Search**: `cart_id`
- **Ordering**: `date_added`

## 🔐 Permissions

- **Authenticated Users**: Can view and manage their own carts and cart items
- **Staff Users**: Can view and manage all carts and cart items
- **Anonymous Users**: Can view carts by session cart_id

## 📝 Usage Examples

### Create a Cart
```bash
POST /api/carts/api/carts/
{
    "cart_id": "my-unique-cart-id"
}
```

### Add Item to Cart
```bash
POST /api/carts/api/cart-items/
{
    "product_id": 1,
    "variation_ids": [1],
    "quantity": 2
}
```

### Update Item Quantity
```bash
POST /api/carts/api/cart-items/1/update_quantity/
{
    "quantity": 3
}
```

### Get Cart Total
```bash
GET /api/carts/api/carts/1/total/
```

## 🚨 Error Handling

The API returns appropriate HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

Validation errors are returned in the following format:
```json
{
    "field_name": ["Error message"]
}
```
