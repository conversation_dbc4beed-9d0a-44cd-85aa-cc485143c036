from django.contrib import admin
from django.contrib.admin import Simple<PERSON>istFilter
from django.db.models import Count, F, Sum
from django.urls import reverse
from django.utils import timezone

from carts.models import Cart, CartItem


class CartItemCountFilter(SimpleListFilter):
    title = "Cart Item Count"
    parameter_name = "item_count"

    def lookups(self, request, model_admin):
        return (
            ("empty", "Empty Carts"),
            ("1-3", "1-3 Items"),
            ("4-10", "4-10 Items"),
            ("10+", "More than 10 Items"),
        )

    def queryset(self, request, queryset):
        if self.value() == "empty":
            return queryset.annotate(item_count=Count("cartitem")).filter(item_count=0)
        elif self.value() == "1-3":
            return queryset.annotate(item_count=Count("cartitem")).filter(
                item_count__range=(1, 3)
            )
        elif self.value() == "4-10":
            return queryset.annotate(item_count=Count("cartitem")).filter(
                item_count__range=(4, 10)
            )
        elif self.value() == "10+":
            return queryset.annotate(item_count=Count("cartitem")).filter(
                item_count__gt=10
            )


class CartAgeFilter(SimpleListFilter):
    title = "Cart Age"
    parameter_name = "cart_age"

    def lookups(self, request, model_admin):
        return (
            ("today", "Today"),
            ("week", "This Week"),
            ("month", "This Month"),
            ("old", "Older than 1 Month"),
        )

    def queryset(self, request, queryset):
        now = timezone.now()
        if self.value() == "today":
            return queryset.filter(date_added__date=now.date())
        elif self.value() == "week":
            week_ago = now - timezone.timedelta(days=7)
            return queryset.filter(date_added__gte=week_ago)
        elif self.value() == "month":
            month_ago = now - timezone.timedelta(days=30)
            return queryset.filter(date_added__gte=month_ago)
        elif self.value() == "old":
            month_ago = now - timezone.timedelta(days=30)
            return queryset.filter(date_added__lt=month_ago)


@admin.register(Cart)
class CartAdmin(admin.ModelAdmin):
    list_display = (
        "cart_id",
        "item_count",
        "total_value",
        "date_added",
        "cart_age",
    )
    search_fields = ("cart_id",)
    ordering = ("-date_added",)
    list_filter = (CartItemCountFilter, CartAgeFilter, "date_added")
    readonly_fields = ("date_added", "cart_summary")
    list_per_page = 10
    date_hierarchy = "date_added"

    fieldsets = (
        ("Cart Information", {"fields": ("cart_id", "date_added", "cart_summary")}),
    )

    actions = ["delete_empty_carts", "delete_old_carts"]

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(
            _item_count=Count("cartitem"),
            _total_value=Sum(F("cartitem__product__price") * F("cartitem__quantity")),
        ).prefetch_related("cartitem_set__product", "cartitem_set__user")
        return queryset

    def item_count(self, obj):
        count = obj._item_count
        if count > 0:
            url = (
                reverse("admin:carts_cartitem_changelist")
                + f"?cart__id__exact={obj.id}"
            )
            return f"{count} items"
        return "Empty"

    item_count.short_description = "Items"
    item_count.admin_order_field = "_item_count"

    def total_value(self, obj):
        total = obj._total_value or 0
        if total > 0:
            return f"Rs. {total:.2f}"
        return "Rs. 0.00"

    total_value.short_description = "Total Value"
    total_value.admin_order_field = "_total_value"

    def cart_age(self, obj):
        now = timezone.now()
        age = now - obj.date_added
        if age.days == 0:
            hours = age.seconds // 3600
            return f"{hours} hours ago"
        elif age.days <= 7:
            return f"{age.days} days ago"
        else:
            return f"{age.days} days ago"

    cart_age.short_description = "Age"

    def cart_summary(self, obj):
        items = obj.cartitem_set.all()
        if not items:
            return "Empty cart"

        summary = []
        for item in items[:5]:
            subtotal = f"Rs. {item.sub_total():.2f}"
            summary.append(f"{item.product.name} x{item.quantity} - {subtotal}")

        if items.count() > 5:
            summary.append(f"... and {items.count() - 5} more items")

        return "\n".join(summary)

    cart_summary.short_description = "Cart Summary"

    def delete_empty_carts(self, request, queryset):
        empty_carts = queryset.annotate(item_count=Count("cartitem")).filter(
            item_count=0
        )
        deleted_count = empty_carts.count()
        empty_carts.delete()
        self.message_user(request, f"{deleted_count} empty carts deleted.")

    delete_empty_carts.short_description = "Delete empty carts"

    def delete_old_carts(self, request, queryset):
        month_ago = timezone.now() - timezone.timedelta(days=30)
        old_carts = queryset.filter(date_added__lt=month_ago)
        deleted_count = old_carts.count()
        old_carts.delete()
        self.message_user(request, f"{deleted_count} old carts (>30 days) deleted.")

    delete_old_carts.short_description = "Delete old carts (older than 30 days)"


class UserFilter(SimpleListFilter):
    title = "User Type"
    parameter_name = "user_type"

    def lookups(self, request, model_admin):
        return (
            ("registered", "Registered Users"),
            ("anonymous", "Anonymous Users"),
        )

    def queryset(self, request, queryset):
        if self.value() == "registered":
            return queryset.filter(user__isnull=False)
        elif self.value() == "anonymous":
            return queryset.filter(user__isnull=True)


class QuantityFilter(SimpleListFilter):
    title = "Quantity Range"
    parameter_name = "quantity_range"

    def lookups(self, request, model_admin):
        return (
            ("1", "Single Item"),
            ("2-5", "2-5 Items"),
            ("6-10", "6-10 Items"),
            ("10+", "More than 10"),
        )

    def queryset(self, request, queryset):
        if self.value() == "1":
            return queryset.filter(quantity=1)
        elif self.value() == "2-5":
            return queryset.filter(quantity__range=(2, 5))
        elif self.value() == "6-10":
            return queryset.filter(quantity__range=(6, 10))
        elif self.value() == "10+":
            return queryset.filter(quantity__gt=10)


@admin.register(CartItem)
class CartItemAdmin(admin.ModelAdmin):
    list_display = (
        "product_name",
        "user_info",
        "cart_id",
        "quantity",
        "subtotal",
        "variations",
        "status",
        "is_active",
    )
    list_filter = ("is_active", UserFilter, QuantityFilter, "product__category")
    search_fields = (
        "product__name",
        "user__email",
        "user__first_name",
        "user__last_name",
        "cart__cart_id",
    )
    list_editable = ("is_active",)
    readonly_fields = ("subtotal", "variations_summary")
    ordering = ("-cart__date_added",)
    list_per_page = 15

    fieldsets = (
        (
            "Item Information",
            {"fields": ("product", "user", "cart", "quantity", "subtotal")},
        ),
        (
            "Variations",
            {"fields": ("variation", "variations_summary"), "classes": ("collapse",)},
        ),
        ("Status", {"fields": ("is_active",)}),
    )

    filter_horizontal = ("variation",)
    actions = ["activate_items", "deactivate_items", "remove_from_cart"]

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related("product", "user", "cart")
            .prefetch_related("variation")
        )

    def product_name(self, obj):
        return obj.product.name

    product_name.short_description = "Product"
    product_name.admin_order_field = "product__name"

    def user_info(self, obj):
        if obj.user:
            display_name = (
                f"{obj.user.first_name} {obj.user.last_name}".strip() or obj.user.email
            )
            return display_name
        return "Anonymous"

    user_info.short_description = "User"
    user_info.admin_order_field = "user__email"

    def cart_id(self, obj):
        if obj.cart:
            cart_display = (
                obj.cart.cart_id[:15] + "..."
                if len(obj.cart.cart_id) > 15
                else obj.cart.cart_id
            )
            return cart_display
        return "No Cart"

    cart_id.short_description = "Cart"
    cart_id.admin_order_field = "cart__cart_id"

    def quantity(self, obj):
        return str(obj.quantity)

    quantity.short_description = "Qty"
    quantity.admin_order_field = "quantity"

    def subtotal(self, obj):
        subtotal = obj.sub_total()
        return f"Rs. {subtotal:.2f}"

    subtotal.short_description = "Subtotal"

    def variations(self, obj):
        variations = obj.variation.all()
        if variations:
            var_list = [
                f"{v.variation_category}: {v.variation_value}" for v in variations[:2]
            ]
            display = ", ".join(var_list)
            if variations.count() > 2:
                display += f" (+{variations.count() - 2} more)"
            return display
        return "No variations"

    variations.short_description = "Variations"

    def variations_summary(self, obj):
        variations = obj.variation.all()
        if not variations:
            return "No variations selected"

        summary = []
        for variation in variations:
            summary.append(f"{variation.variation_category}: {variation.variation_value}")

        return "\n".join(summary)

    variations_summary.short_description = "Variation Details"

    def status(self, obj):
        if obj.is_active:
            return "Active"
        return "Inactive"

    status.short_description = "Status"
    status.admin_order_field = "is_active"

    def activate_items(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f"{updated} cart items activated.")

    activate_items.short_description = "Activate selected cart items"

    def deactivate_items(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f"{updated} cart items deactivated.")

    deactivate_items.short_description = "Deactivate selected cart items"

    def remove_from_cart(self, request, queryset):
        deleted_count = queryset.count()
        queryset.delete()
        self.message_user(request, f"{deleted_count} cart items removed.")

    remove_from_cart.short_description = "Remove selected items from cart"
