JAZZMIN_SETTINGS = {
    "site_title": "Mah<PERSON>eau<PERSON>",
    "site_header": "Mah<PERSON>eauty",
    "site_brand": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    "welcome_sign": "Welcome to MahBeauty Admin Panel",
    "copyright": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    "site_logo": "img/logo.png",
    "login_logo": "img/login_logo.png",
    # Custom CSS for login logo sizing
    "custom_css": "css/admin_custom.css",
    # Menu ordering
    "order_with_respect_to": [
        "account",
        "store",
        "products",
        "orders",
        "carts",
        "social_django",
        "token_blacklist",
    ],
    # Hide authentication and authorization apps completely
    "hide_apps": ["auth", "contenttypes", "sessions"],
    # Top menu links for quick access with better naming
    "topmenu_links": [
        {"name": "Dashboard", "url": "admin:index", "permissions": ["auth.view_user"]},
        {
            "name": "Add Product",
            "url": "admin:products_product_add",
            "permissions": ["products.add_product"],
        },
        {
            "name": "View Orders",
            "url": "admin:orders_order_changelist",
            "permissions": ["orders.view_order"],
        },
        {
            "name": "Shopping Carts",
            "url": "admin:carts_cart_changelist",
            "permissions": ["carts.view_cart"],
        },
        {"model": "account.UserAccount"},
    ],
    # Hide only authentication and authorization models to avoid duplicates
    "hide_models": [
        # Hide authentication and authorization models
        "auth.User",
        "auth.Group",
        "auth.Permission",
        "contenttypes.ContentType",
        "sessions.Session",
    ],
    # Icons for apps and models
    "icons": {
        "account": "fas fa-users-cog",
        "account.UserAccount": "fas fa-user",
        "account.StaffUserAccount": "fas fa-user-tie",
        "account.SuperuserAccount": "fas fa-user-shield",
        "products": "fas fa-shopping-bag",
        "products.ProductCategory": "fas fa-tags",
        "products.Product": "fas fa-box",
        "products.Variation": "fas fa-palette",
        "products.ReviewRating": "fas fa-star",
        "orders": "fas fa-shopping-cart",
        "orders.Order": "fas fa-receipt",
        "orders.OrderProduct": "fas fa-list",
        "orders.Payment": "fas fa-credit-card",
        "carts": "fas fa-shopping-basket",
        "carts.Cart": "fas fa-shopping-basket",
        "carts.CartItem": "fas fa-plus-square",
        "store": "fas fa-store",
        "store.Organization": "fas fa-building",
        "store.HeroSlide": "fas fa-images",
        "store.HeroSlideImage": "fas fa-image",
        "store.AboutPageContent": "fas fa-info-circle",
        "store.Testimonial": "fas fa-quote-left",
        "social_django": "fab fa-google",
        "social_django.UserSocialAuth": "fas fa-users",
        "social_django.Nonce": "fas fa-key",
        "social_django.Association": "fas fa-link",
        "token_blacklist": "fas fa-shield-alt",
        "token_blacklist.BlacklistedToken": "fas fa-ban",
        "token_blacklist.OutstandingToken": "fas fa-key",
    },
    # UI Settings
    "show_sidebar": True,
    "navigation_expanded": True,
    # "changeform_format": "vertical_tabs",
    "related_modal_active": False,
}

JAZZMIN_UI_TWEAKS = {
    # "theme": "sketchy",
    "navbar_small_text": True,
    "footer_small_text": True,
    "body_small_text": False,
    "brand_small_text": False,
    "brand_colour": False,
    "accent": "accent-primary",
    "navbar": "navbar-dark",
    "no_navbar_border": True,
    "navbar_fixed": True,
    "layout_boxed": False,
    "footer_fixed": True,
    "sidebar_fixed": False,
    "sidebar": "sidebar-dark-primary",
    "sidebar_nav_small_text": False,
    "sidebar_disable_expand": False,
    "sidebar_nav_child_indent": False,
    "sidebar_nav_compact_style": False,
    "sidebar_nav_legacy_style": True,
    "sidebar_nav_flat_style": False,
    "dark_mode_theme": None,
    "button_classes": {
        "primary": "btn-primary",
        "secondary": "btn-secondary",
        "info": "btn-info",
        "warning": "btn-warning",
        "danger": "btn-danger",
        "success": "btn-success",
    },
}
