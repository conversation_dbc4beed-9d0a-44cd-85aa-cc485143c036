"""
Utility functions for order management, including email notifications
"""

import logging
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils.html import strip_tags

logger = logging.getLogger(__name__)

User = get_user_model()


def get_admin_emails():
    """
    Get list of admin email addresses to send notifications to.
    Only includes emails from settings.ADMINS (Gmail admin email).
    """
    admin_emails = []

    # Only include emails from settings.ADMINS (Gmail admin email)
    if hasattr(settings, "ADMINS"):
        admin_emails = [email for name, email in settings.ADMINS if email]

    # Remove duplicates
    admin_emails = list(set(admin_emails))

    return admin_emails


def send_order_notification_email(order):
    """
    Send email notification to admin when a new order is placed.

    Args:
        order: Order instance
    """
    admin_emails = get_admin_emails()

    if not admin_emails:
        logger.warning("No admin emails configured for order notifications")
        return False

    try:
        # Prepare context for email template
        context = {
            "order": order,
            "site_name": getattr(settings, "SITE_NAME", "MahBeauty"),
            "domain": getattr(settings, "DOMAIN", "localhost:8000"),
        }

        # Render email templates
        subject = f"New Order Received - #{order.order_number}"
        html_message = render_to_string(
            "orders/emails/admin_order_notification.html", context
        )
        plain_message = render_to_string(
            "orders/emails/admin_order_notification.txt", context
        )

        # Create email message
        email = EmailMultiAlternatives(
            subject=subject,
            body=plain_message,
            from_email=getattr(settings, "DEFAULT_FROM_EMAIL", "<EMAIL>"),
            to=admin_emails,
        )
        email.attach_alternative(html_message, "text/html")

        # Send email
        email.send()

        logger.info(
            f"Order notification email sent to {len(admin_emails)} admin(s) for order {order.order_number}"
        )
        return True

    except Exception as e:
        logger.error(
            f"Failed to send order notification email for order {order.order_number}: {e}"
        )
        return False


def send_order_confirmation_email(order):
    """
    Send order confirmation email to customer.

    Args:
        order: Order instance
    """
    if not order.email:
        logger.warning(f"No email address for order {order.order_number}")
        return False

    try:
        # Prepare context for email template
        context = {
            "order": order,
            "customer_name": order.full_name(),
            "site_name": getattr(settings, "SITE_NAME", "MahBeauty"),
            "domain": getattr(settings, "DOMAIN", "localhost:8000"),
        }

        # Render email templates
        subject = f"Order Confirmation - #{order.order_number}"
        html_message = render_to_string(
            "orders/emails/customer_order_confirmation.html", context
        )
        plain_message = render_to_string(
            "orders/emails/customer_order_confirmation.txt", context
        )

        # Create email message
        email = EmailMultiAlternatives(
            subject=subject,
            body=plain_message,
            from_email=getattr(settings, "DEFAULT_FROM_EMAIL", "<EMAIL>"),
            to=[order.email],
        )
        email.attach_alternative(html_message, "text/html")

        # Send email
        email.send()

        logger.info(
            f"Order confirmation email sent to {order.email} for order {order.order_number}"
        )
        return True

    except Exception as e:
        logger.error(
            f"Failed to send order confirmation email for order {order.order_number}: {e}"
        )
        return False
