<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - {{ order.order_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #28a745;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .order-details {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .order-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 20px;
        }
        .info-item {
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 3px;
        }
        .info-label {
            font-weight: bold;
            color: #666;
        }
        .shipping-details {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .footer {
            text-align: center;
            color: #666;
            font-size: 12px;
            margin-top: 30px;
        }
        .thank-you {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>✅ Order Confirmed!</h1>
        <p>Thank you for your order, {{ customer_name }}!</p>
    </div>

    <div class="thank-you">
        <p><strong>Your order has been successfully placed and is being processed.</strong></p>
        <p>We'll send you updates as your order progresses.</p>
    </div>

    <div class="order-details">
        <h2>Order Summary</h2>
        
        <div class="order-info">
            <div class="info-item">
                <div class="info-label">Order Number:</div>
                <div><strong>{{ order.order_number }}</strong></div>
            </div>
            <div class="info-item">
                <div class="info-label">Order Date:</div>
                <div>{{ order.created_at|date:"F d, Y H:i" }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Status:</div>
                <div>{{ order.status }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Total Amount:</div>
                <div><strong>₹{{ order.grand_total }}</strong></div>
            </div>
        </div>

        <div class="shipping-details">
            <h3>Shipping Address</h3>
            <p><strong>{{ order.full_name }}</strong></p>
            <p>{{ order.address }}</p>
            <p>{{ order.area }}, {{ order.state }}</p>
            <p>Phone: {{ order.phone }}</p>
        </div>

        {% if order.payment %}
        <div class="info-item">
            <div class="info-label">Payment Method:</div>
            <div>{{ order.payment.payment_method.name|default:"Not specified" }}</div>
        </div>
        {% endif %}

        {% if order.order_note %}
        <div class="info-item">
            <div class="info-label">Order Note:</div>
            <div>{{ order.order_note }}</div>
        </div>
        {% endif %}
    </div>

    <div class="footer">
        <p>Thank you for shopping with {{ site_name }}!</p>
        <p>If you have any questions about your order, please contact our customer service.</p>
        <p>This is an automated confirmation email.</p>
    </div>
</body>
</html>
