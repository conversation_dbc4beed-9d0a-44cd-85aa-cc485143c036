<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Order Notification - {{ order.order_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .order-details {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .order-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 20px;
        }
        .info-item {
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 3px;
        }
        .info-label {
            font-weight: bold;
            color: #666;
        }
        .customer-details {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .footer {
            text-align: center;
            color: #666;
            font-size: 12px;
            margin-top: 30px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛍️ New Order Received!</h1>
        <p>A new order has been placed on {{ site_name }}</p>
    </div>

    <div class="order-details">
        <h2>Order Details</h2>
        
        <div class="order-info">
            <div class="info-item">
                <div class="info-label">Order Number:</div>
                <div><strong>{{ order.order_number }}</strong></div>
            </div>
            <div class="info-item">
                <div class="info-label">Order Date:</div>
                <div>{{ order.created_at|date:"F d, Y H:i" }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Status:</div>
                <div>{{ order.status }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Total Amount:</div>
                <div><strong>₹{{ order.grand_total }}</strong></div>
            </div>
        </div>

        <div class="customer-details">
            <h3>Customer Information</h3>
            <p><strong>Name:</strong> {{ order.full_name }}</p>
            <p><strong>Email:</strong> {{ order.email }}</p>
            <p><strong>Phone:</strong> {{ order.phone }}</p>
            <p><strong>Address:</strong> {{ order.address }}, {{ order.area }}, {{ order.state }}</p>
            {% if order.order_note %}
            <p><strong>Order Note:</strong> {{ order.order_note }}</p>
            {% endif %}
        </div>

        {% if order.payment %}
        <div class="info-item">
            <div class="info-label">Payment Method:</div>
            <div>{{ order.payment.payment_method.name|default:"Not specified" }}</div>
        </div>
        {% endif %}
    </div>

    <div style="text-align: center;">
        <a href="http://{{ domain }}/admin/orders/order/{{ order.id }}/change/" class="btn">
            View Order in Admin Panel
        </a>
    </div>

    <div class="footer">
        <p>This is an automated notification from {{ site_name }}.</p>
        <p>Please log in to the admin panel to manage this order.</p>
    </div>
</body>
</html>
