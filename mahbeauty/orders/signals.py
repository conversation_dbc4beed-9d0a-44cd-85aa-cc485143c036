import logging
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.contrib.auth import get_user_model

from orders.models import Order
from orders.utils import send_order_notification_email, send_order_confirmation_email
from orders.notifications import order_notification_manager

logger = logging.getLogger(__name__)

User = get_user_model()


@receiver(post_save, sender=Order)
def order_created_notification(sender, instance, created, **kwargs):
    """
    Signal to send notifications when a new order is created.
    Sends email to admin and logs the order creation.
    """
    if created:
        logger.info(f"New order created: {instance.order_number} by {instance.email}")

        # Send all order creation notifications
        try:
            notifications_sent = order_notification_manager.notify_order_created(instance)
            logger.info(f"Order notifications sent for {instance.order_number}: {notifications_sent}")
        except Exception as e:
            logger.error(f"Failed to send order notifications for {instance.order_number}: {e}")


# Store original status before save
@receiver(pre_save, sender=Order)
def store_original_status(sender, instance, **kwargs):
    """Store the original status before saving to detect changes"""
    if instance.pk:
        try:
            original = Order.objects.get(pk=instance.pk)
            instance._original_status = original.status
        except Order.DoesNotExist:
            instance._original_status = None
    else:
        instance._original_status = None


@receiver(post_save, sender=Order)
def order_status_changed_notification(sender, instance, created, **kwargs):
    """
    Signal to send notifications when order status changes.
    """
    if not created and hasattr(instance, '_original_status') and instance._original_status:
        old_status = instance._original_status
        new_status = instance.status

        if old_status != new_status:
            logger.info(f"Order {instance.order_number} status changed: {old_status} -> {new_status}")

            # Send status change notifications
            try:
                notifications_sent = order_notification_manager.notify_order_status_changed(
                    instance, old_status, new_status
                )
                logger.info(f"Status change notifications sent for {instance.order_number}: {notifications_sent}")
            except Exception as e:
                logger.error(f"Failed to send status change notifications for {instance.order_number}: {e}")
