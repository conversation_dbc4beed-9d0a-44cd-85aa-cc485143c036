from django.contrib import admin
from django.contrib.admin import Simple<PERSON>istFilter
from django.db.models import Count, Sum
from django.urls import reverse

from orders.models import Order, OrderProduct, Payment, PaymentMethod


@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    list_display = ("name", "image_display", "is_active")
    list_filter = ("is_active",)
    search_fields = ("name",)
    list_editable = ("is_active",)
    ordering = ("name",)

    def image_display(self, obj):
        if obj.image:
            return f"Image: {obj.image.name}"
        return "No Image"

    image_display.short_description = "Image"


class PaymentMethodFilter(SimpleListFilter):
    title = "Payment Method"
    parameter_name = "payment_method"

    def lookups(self, request, model_admin):
        payment_methods = PaymentMethod.objects.filter(is_active=True)
        return [(pm.id, pm.name) for pm in payment_methods]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(payment_method_id=self.value())


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = (
        "payment_id",
        "user_info",
        "payment_method",
        "order_count",
        "total_amount",
    )
    list_filter = (PaymentMethodFilter,)
    search_fields = ("payment_id", "user__email", "user__first_name", "user__last_name")
    readonly_fields = ("order_summary", "payment_stats")
    ordering = ("-id",)
    list_per_page = 25

    fieldsets = (
        ("Payment Information", {"fields": ("user", "payment_id", "payment_method")}),
        (
            "Payment Summary",
            {"fields": ("order_summary", "payment_stats"), "classes": ("collapse",)},
        ),
    )

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = (
            queryset.select_related("user")
            .annotate(
                _order_count=Count("order"), _total_amount=Sum("order__grand_total")
            )
            .prefetch_related("order_set")
        )
        return queryset

    def user_info(self, obj):
        if obj.user:
            display_name = (
                f"{obj.user.first_name} {obj.user.last_name}".strip() or obj.user.email
            )
            return display_name
        return "No User"

    user_info.short_description = "User"
    user_info.admin_order_field = "user__email"

    def payment_method(self, obj):
        if obj.payment_method:
            return obj.payment_method.name
        return "No Payment Method"

    payment_method.short_description = "Payment Method"
    payment_method.admin_order_field = "payment_method"

    def order_count(self, obj):
        count = obj._order_count
        if count > 0:
            return f"{count} orders"
        return "No orders"

    order_count.short_description = "Orders"
    order_count.admin_order_field = "_order_count"

    def total_amount(self, obj):
        total = obj._total_amount or 0
        if total > 0:
            return f"Rs. {total:,}"
        return "Rs. 0"

    total_amount.short_description = "Total Amount"
    total_amount.admin_order_field = "_total_amount"

    def order_summary(self, obj):
        orders = obj.order_set.all()
        if not orders:
            return "No orders"

        summary = []
        for order in orders[:5]:
            grand_total = f"Rs. {order.grand_total:,}"
            summary.append(f"{order.order_number} - {order.status} - {grand_total}")

        if orders.count() > 5:
            summary.append(f"... and {orders.count() - 5} more orders")

        return "\n".join(summary)

    order_summary.short_description = "Order Summary"

    def payment_stats(self, obj):
        orders = obj.order_set.all()
        if not orders:
            return "No statistics available"

        total_orders = orders.count()
        delivered_orders = orders.filter(status="Delivered").count()
        total_amount = sum(order.grand_total for order in orders)

        total_amount_formatted = f"Rs. {total_amount:,}"
        success_rate = f"{(delivered_orders / total_orders * 100):.1f}%"

        stats = f"""Total Orders: {total_orders}
Delivered: {delivered_orders}
Total Amount: {total_amount_formatted}
Success Rate: {success_rate}"""
        return stats

    payment_stats.short_description = "Payment Statistics"


class OrderStatusFilter(SimpleListFilter):
    title = "Order Status"
    parameter_name = "order_status"

    def lookups(self, request, model_admin):
        return (
            ("Processing", "Processing Orders"),
            ("Packed", "Packed Orders"),
            ("Shipped", "Shipped Orders"),
            ("Delivered", "Delivered Orders"),
            ("Cancel", "Cancelled Orders"),
        )

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(status=self.value())


class OrderValueFilter(SimpleListFilter):
    title = "Order Value"
    parameter_name = "order_value"

    def lookups(self, request, model_admin):
        return (
            ("low", "Under Rs. 1,000"),
            ("medium", "Rs. 1,000 - 5,000"),
            ("high", "Rs. 5,000 - 10,000"),
            ("premium", "Over Rs. 10,000"),
        )

    def queryset(self, request, queryset):
        if self.value() == "low":
            return queryset.filter(grand_total__lt=1000)
        elif self.value() == "medium":
            return queryset.filter(grand_total__range=(1000, 5000))
        elif self.value() == "high":
            return queryset.filter(grand_total__range=(5000, 10000))
        elif self.value() == "premium":
            return queryset.filter(grand_total__gt=10000)


class OrderProductInline(admin.TabularInline):
    model = OrderProduct
    extra = 0
    fields = ("product", "quantity", "product_price", "subtotal", "ordered")
    readonly_fields = ("subtotal",)
    classes = ("collapse",)

    def subtotal(self, obj):
        if obj.pk:
            subtotal = obj.quantity * obj.product_price
            return f"Rs. {subtotal:,}"
        return "-"

    subtotal.short_description = "Subtotal"


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = (
        "order_number",
        "customer_info",
        "status",
        "payment_method",
        "grand_total",
        "order_items_count",
        "is_ordered",
        "created_at",
    )
    list_filter = (
        OrderStatusFilter,
        OrderValueFilter,
        "is_ordered",
        PaymentMethodFilter,
        "created_at",
    )
    search_fields = (
        "order_number",
        "first_name",
        "last_name",
        "email",
        "phone",
        "user__email",
    )
    list_editable = ("status",)
    readonly_fields = (
        "created_at",
        "upated_at",
        "order_summary",
        "customer_details",
        "shipping_address",
    )
    ordering = ("-created_at",)
    list_per_page = 20
    date_hierarchy = "created_at"

    fieldsets = (
        (
            "Order Information",
            {"fields": ("order_number", "user", "payment", "status", "is_ordered")},
        ),
        (
            "Customer Information",
            {
                "fields": (
                    "first_name",
                    "last_name",
                    "email",
                    "phone",
                    "customer_details",
                )
            },
        ),
        (
            "Shipping Address",
            {
                "fields": ("state", "area", "address", "shipping_address"),
                "classes": ("collapse",),
            },
        ),
        (
            "Order Details",
            {"fields": ("grand_total", "tax", "order_note", "order_summary")},
        ),
        (
            "Technical Details",
            {"fields": ("ip", "created_at", "upated_at"), "classes": ("collapse",)},
        ),
    )

    inlines = [OrderProductInline]
    actions = [
        "mark_as_packed",
        "mark_as_shipped",
        "mark_as_delivered",
        "mark_as_cancelled",
        "mark_as_ordered",
    ]

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = (
            queryset.select_related("user", "payment")
            .annotate(_items_count=Count("orderproduct"))
            .prefetch_related("orderproduct_set__product")
        )
        return queryset

    def customer_info(self, obj):
        name = obj.full_name()
        return f"{name}\n{obj.email}"

    customer_info.short_description = "Customer"
    customer_info.admin_order_field = "first_name"

    def payment_method(self, obj):
        if obj.payment and obj.payment.payment_method:
            return obj.payment.payment_method.name
        return "No Payment"

    payment_method.short_description = "Payment Method"

    def grand_total(self, obj):
        return f"Rs. {obj.grand_total:,}"

    grand_total.short_description = "Total"
    grand_total.admin_order_field = "grand_total"

    def order_items_count(self, obj):
        count = obj._items_count
        if count > 0:
            return f"{count} items"
        return "No items"

    order_items_count.short_description = "Items"
    order_items_count.admin_order_field = "_items_count"

    def customer_details(self, obj):
        details = f"""Name: {obj.full_name()}
Email: {obj.email}
Phone: {obj.phone}
IP Address: {obj.ip or "Not recorded"}"""
        return details

    customer_details.short_description = "Customer Details"

    def shipping_address(self, obj):
        address = f"""State: {obj.state}
Area: {obj.area}
Address: {obj.address}"""
        return address

    shipping_address.short_description = "Shipping Address"

    def order_summary(self, obj):
        items = obj.orderproduct_set.all()
        if not items:
            return "No items in order"

        summary = []
        total_items = 0
        for item in items:
            total_items += item.quantity
            item_subtotal = f"Rs. {item.quantity * item.product_price:,}"
            summary.append(f"{item.product.name} x{item.quantity} - {item_subtotal}")

        tax_formatted = f"Rs. {obj.tax:,.2f}"
        grand_total_formatted = f"Rs. {obj.grand_total:,}"

        summary.append(f"Total Items: {total_items}")
        summary.append(f"Tax: {tax_formatted}")
        summary.append(f"Grand Total: {grand_total_formatted}")

        return "\n".join(summary)

    order_summary.short_description = "Order Summary"

    # Custom actions
    def mark_as_packed(self, request, queryset):
        updated = queryset.update(status="Packed")
        self.message_user(request, f"{updated} orders marked as packed.")

    mark_as_packed.short_description = "Mark selected orders as packed"

    def mark_as_shipped(self, request, queryset):
        updated = queryset.update(status="Shipped")
        self.message_user(request, f"{updated} orders marked as shipped.")

    mark_as_shipped.short_description = "Mark selected orders as shipped"

    def mark_as_delivered(self, request, queryset):
        updated = queryset.update(status="Delivered")
        self.message_user(request, f"{updated} orders marked as delivered.")

    mark_as_delivered.short_description = "Mark selected orders as delivered"

    def mark_as_cancelled(self, request, queryset):
        updated = queryset.update(status="Cancel")
        self.message_user(request, f"{updated} orders marked as cancelled.")

    mark_as_cancelled.short_description = "Mark selected orders as cancelled"

    def mark_as_ordered(self, request, queryset):
        updated = queryset.update(is_ordered=True)
        self.message_user(request, f"{updated} orders marked as ordered.")

    mark_as_ordered.short_description = "Mark selected orders as ordered"


class OrderProductStatusFilter(SimpleListFilter):
    title = "Order Status"
    parameter_name = "order_status"

    def lookups(self, request, model_admin):
        return (
            ("ordered", "Ordered Items"),
            ("not_ordered", "Not Ordered Items"),
        )

    def queryset(self, request, queryset):
        if self.value() == "ordered":
            return queryset.filter(ordered=True)
        elif self.value() == "not_ordered":
            return queryset.filter(ordered=False)


@admin.register(OrderProduct)
class OrderProductAdmin(admin.ModelAdmin):
    list_display = (
        "product_name",
        "order_number",
        "user_info",
        "quantity",
        "unit_price",
        "subtotal",
        "variations",
        "ordered_status",
        "ordered",
        "created_at",
    )
    list_filter = (
        OrderProductStatusFilter,
        "ordered",
        "product__category",
        "created_at",
    )
    search_fields = (
        "product__name",
        "order__order_number",
        "user__email",
        "user__first_name",
        "user__last_name",
    )
    list_editable = ("ordered",)
    readonly_fields = (
        "created_at",
        "updated_at",
        "subtotal",
        "variations_summary",
    )
    ordering = ("-created_at",)
    list_per_page = 25
    date_hierarchy = "created_at"

    fieldsets = (
        (
            "Product Information",
            {"fields": ("product", "quantity", "product_price", "subtotal")},
        ),
        ("Order Information", {"fields": ("order", "user", "payment", "ordered")}),
        (
            "Variations",
            {"fields": ("variation", "variations_summary"), "classes": ("collapse",)},
        ),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    filter_horizontal = ("variation",)
    actions = ["mark_as_ordered", "mark_as_not_ordered"]

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related("product", "order", "user", "payment")
            .prefetch_related("variation")
        )

    def product_name(self, obj):
        return obj.product.name

    product_name.short_description = "Product"
    product_name.admin_order_field = "product__name"

    def order_number(self, obj):
        return obj.order.order_number

    order_number.short_description = "Order"
    order_number.admin_order_field = "order__order_number"

    def user_info(self, obj):
        display_name = (
            f"{obj.user.first_name} {obj.user.last_name}".strip() or obj.user.email
        )
        return display_name

    user_info.short_description = "User"
    user_info.admin_order_field = "user__email"

    def quantity(self, obj):
        return str(obj.quantity)

    quantity.short_description = "Qty"
    quantity.admin_order_field = "quantity"

    def unit_price(self, obj):
        return f"Rs. {obj.product_price:,}"

    unit_price.short_description = "Unit Price"
    unit_price.admin_order_field = "product_price"

    def subtotal(self, obj):
        subtotal = obj.quantity * obj.product_price
        return f"Rs. {subtotal:,}"

    subtotal.short_description = "Subtotal"

    def variations(self, obj):
        variations = obj.variation.all()
        if variations:
            var_list = [
                f"{v.variation_category}: {v.variation_value}" for v in variations[:2]
            ]
            display = ", ".join(var_list)
            if variations.count() > 2:
                display += f" (+{variations.count() - 2} more)"
            return display
        return "No variations"

    variations.short_description = "Variations"

    def variations_summary(self, obj):
        variations = obj.variation.all()
        if not variations:
            return "No variations selected"

        summary = []
        for variation in variations:
            summary.append(f"{variation.variation_category}: {variation.variation_value}")

        return "\n".join(summary)

    variations_summary.short_description = "Variation Details"

    def ordered_status(self, obj):
        if obj.ordered:
            return "Ordered"
        return "Not Ordered"

    ordered_status.short_description = "Status"
    ordered_status.admin_order_field = "ordered"

    def mark_as_ordered(self, request, queryset):
        updated = queryset.update(ordered=True)
        self.message_user(request, f"{updated} order products marked as ordered.")

    mark_as_ordered.short_description = "Mark selected items as ordered"

    def mark_as_not_ordered(self, request, queryset):
        updated = queryset.update(ordered=False)
        self.message_user(request, f"{updated} order products marked as not ordered.")

    mark_as_not_ordered.short_description = "Mark selected items as not ordered"
