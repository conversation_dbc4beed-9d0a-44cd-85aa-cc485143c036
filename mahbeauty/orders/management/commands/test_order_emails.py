"""
Management command to test order notification emails
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from orders.models import Order, Payment, PaymentMethod
from orders.utils import send_order_notification_email, send_order_confirmation_email

User = get_user_model()


class Command(BaseCommand):
    help = 'Test order notification emails by sending test emails for existing orders'

    def add_arguments(self, parser):
        parser.add_argument(
            '--order-id',
            type=int,
            help='Specific order ID to test email for',
        )
        parser.add_argument(
            '--admin-only',
            action='store_true',
            help='Send only admin notification email',
        )
        parser.add_argument(
            '--customer-only',
            action='store_true',
            help='Send only customer confirmation email',
        )

    def handle(self, *args, **options):
        order_id = options.get('order_id')
        admin_only = options.get('admin_only')
        customer_only = options.get('customer_only')

        if order_id:
            try:
                order = Order.objects.get(id=order_id)
                self.test_emails_for_order(order, admin_only, customer_only)
            except Order.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Order with ID {order_id} does not exist')
                )
                return
        else:
            # Get the most recent order
            order = Order.objects.order_by('-created_at').first()
            if not order:
                self.stdout.write(
                    self.style.ERROR('No orders found in the database')
                )
                return
            
            self.stdout.write(f'Using most recent order: {order.order_number}')
            self.test_emails_for_order(order, admin_only, customer_only)

    def test_emails_for_order(self, order, admin_only=False, customer_only=False):
        """Test emails for a specific order"""
        self.stdout.write(f'\nTesting emails for order: {order.order_number}')
        self.stdout.write(f'Customer: {order.full_name()} ({order.email})')
        self.stdout.write(f'Total: ₹{order.grand_total}')
        self.stdout.write('-' * 50)

        success_count = 0
        total_count = 0

        if not customer_only:
            # Test admin notification email
            self.stdout.write('Sending admin notification email...')
            total_count += 1
            try:
                if send_order_notification_email(order):
                    self.stdout.write(
                        self.style.SUCCESS('✓ Admin notification email sent successfully')
                    )
                    success_count += 1
                else:
                    self.stdout.write(
                        self.style.ERROR('✗ Failed to send admin notification email')
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'✗ Error sending admin notification email: {e}')
                )

        if not admin_only:
            # Test customer confirmation email
            self.stdout.write('Sending customer confirmation email...')
            total_count += 1
            try:
                if send_order_confirmation_email(order):
                    self.stdout.write(
                        self.style.SUCCESS('✓ Customer confirmation email sent successfully')
                    )
                    success_count += 1
                else:
                    self.stdout.write(
                        self.style.ERROR('✗ Failed to send customer confirmation email')
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'✗ Error sending customer confirmation email: {e}')
                )

        self.stdout.write('-' * 50)
        self.stdout.write(f'Email test completed: {success_count}/{total_count} emails sent successfully')
        
        if success_count == 0:
            self.stdout.write(
                self.style.WARNING(
                    '\nNote: If using console email backend, check the console output for email content.'
                )
            )
