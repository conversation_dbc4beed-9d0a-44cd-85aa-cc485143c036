"""
Notification system for orders
"""

import logging
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings

logger = logging.getLogger(__name__)

User = get_user_model()


class OrderNotificationManager:
    """
    Manager class for handling order notifications
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def notify_order_created(self, order):
        """
        Send all notifications when an order is created
        
        Args:
            order: Order instance
        """
        notifications_sent = []
        
        # Send admin notification
        if self._send_admin_notification(order):
            notifications_sent.append('admin_email')
        
        # Send customer confirmation
        if self._send_customer_confirmation(order):
            notifications_sent.append('customer_email')
        
        # Log in-app notification for admin users
        if self._create_admin_notification(order):
            notifications_sent.append('admin_notification')
        
        self.logger.info(
            f"Order {order.order_number} notifications sent: {', '.join(notifications_sent)}"
        )
        
        return notifications_sent
    
    def _send_admin_notification(self, order):
        """Send email notification to admin"""
        try:
            from orders.utils import send_order_notification_email
            return send_order_notification_email(order)
        except Exception as e:
            self.logger.error(f"Failed to send admin notification for order {order.order_number}: {e}")
            return False
    
    def _send_customer_confirmation(self, order):
        """Send confirmation email to customer"""
        try:
            from orders.utils import send_order_confirmation_email
            return send_order_confirmation_email(order)
        except Exception as e:
            self.logger.error(f"Failed to send customer confirmation for order {order.order_number}: {e}")
            return False
    
    def _create_admin_notification(self, order):
        """Create in-app notification for admin users"""
        try:
            # Get all staff users
            admin_users = User.objects.filter(is_staff=True, is_active=True)
            
            notification_message = (
                f"New order #{order.order_number} received from {order.full_name()} "
                f"for Rs.{order.grand_total}"
            )
            
            # Log the notification (in a real app, you might save this to a Notification model)
            for admin_user in admin_users:
                self.logger.info(
                    f"In-app notification for {admin_user.email}: {notification_message}"
                )
            
            return True
        except Exception as e:
            self.logger.error(f"Failed to create admin notification for order {order.order_number}: {e}")
            return False
    
    def notify_order_status_changed(self, order, old_status, new_status):
        """
        Send notifications when order status changes
        
        Args:
            order: Order instance
            old_status: Previous status
            new_status: New status
        """
        notifications_sent = []
        
        # Send customer notification about status change
        if self._send_status_change_notification(order, old_status, new_status):
            notifications_sent.append('customer_status_email')
        
        self.logger.info(
            f"Order {order.order_number} status change notifications sent: {', '.join(notifications_sent)}"
        )
        
        return notifications_sent
    
    def _send_status_change_notification(self, order, old_status, new_status):
        """Send email notification to customer about status change"""
        try:
            if not order.email:
                return False
            
            subject = f"Order Status Update - #{order.order_number}"
            
            context = {
                'order': order,
                'old_status': old_status,
                'new_status': new_status,
                'customer_name': order.full_name(),
                'site_name': getattr(settings, 'SITE_NAME', 'MahBeauty'),
            }
            
            message = render_to_string('orders/emails/order_status_update.txt', context)
            
            send_mail(
                subject=subject,
                message=message,
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
                recipient_list=[order.email],
                fail_silently=False,
            )
            
            self.logger.info(
                f"Status change notification sent to {order.email} for order {order.order_number}: "
                f"{old_status} -> {new_status}"
            )
            return True
            
        except Exception as e:
            self.logger.error(
                f"Failed to send status change notification for order {order.order_number}: {e}"
            )
            return False


# Global instance
order_notification_manager = OrderNotificationManager()
