# Order Notifications System

This guide explains the comprehensive order notification system implemented in MahBeauty that automatically sends emails and notifications when orders are placed or their status changes.

## 🚀 Features

### Automatic Notifications
- **Admin Email Notifications**: Sent to all staff users when new orders are placed
- **Customer Confirmation Emails**: Sent to customers when they place orders
- **Status Update Notifications**: Sent to customers when order status changes
- **In-app Notifications**: Logged for admin users (can be extended to a full notification system)

### Email Templates
- Professional HTML and plain text email templates
- Responsive design for mobile devices
- Branded with site name and styling
- Comprehensive order information included

## 📧 Email Types

### 1. Admin Order Notification
**Triggered**: When a new order is created
**Recipients**: All staff users
**Content**: 
- Order details (number, date, total)
- Customer information
- Shipping address
- Payment method
- Direct link to admin panel

### 2. Customer Order Confirmation
**Triggered**: When a new order is created
**Recipients**: Customer who placed the order
**Content**:
- Order summary
- Shipping address
- Payment information
- Thank you message

### 3. Order Status Update
**Triggered**: When order status changes
**Recipients**: Customer who placed the order
**Content**:
- Status change information
- Order details
- Contextual messages based on new status

## ⚙️ Configuration

### Email Settings
Add these environment variables for production email sending:

```bash
# Email Backend (use SMTP for production)
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend

# SMTP Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password

# Default sender email
DEFAULT_FROM_EMAIL=<EMAIL>

# Admin email for notifications
ADMIN_EMAIL=<EMAIL>

# Site information
SITE_NAME=MahBeauty
DOMAIN=yourdomain.com
```

### Development Setup
For development, the system uses Django's console email backend by default, which prints emails to the console instead of sending them.

## 🔧 Implementation Details

### Django Signals
The system uses Django signals to automatically trigger notifications:

```python
# orders/signals.py
@receiver(post_save, sender=Order)
def order_created_notification(sender, instance, created, **kwargs):
    if created:
        # Send all order creation notifications
        order_notification_manager.notify_order_created(instance)
```

### Notification Manager
The `OrderNotificationManager` class handles all notification logic:

```python
# orders/notifications.py
class OrderNotificationManager:
    def notify_order_created(self, order):
        # Send admin notification
        # Send customer confirmation
        # Create in-app notification
```

### Email Utilities
Utility functions handle email sending with error handling:

```python
# orders/utils.py
def send_order_notification_email(order):
    # Send HTML and plain text emails to admins
    
def send_order_confirmation_email(order):
    # Send confirmation email to customer
```

## 🧪 Testing

### Management Command
Test the email system using the management command:

```bash
# Test emails for the most recent order
python manage.py test_order_emails

# Test emails for a specific order
python manage.py test_order_emails --order-id 123

# Test only admin notifications
python manage.py test_order_emails --admin-only

# Test only customer confirmations
python manage.py test_order_emails --customer-only
```

### Test Script
Run the comprehensive test script:

```bash
python test_order_notifications.py
```

This script will:
- Show current email configuration
- Test admin notification emails
- Test customer confirmation emails
- Test the notification manager
- Test status change notifications

## 📁 File Structure

```
orders/
├── signals.py                 # Django signals for automatic notifications
├── utils.py                   # Email utility functions
├── notifications.py           # Notification manager class
├── templates/
│   └── orders/
│       └── emails/
│           ├── admin_order_notification.html
│           ├── admin_order_notification.txt
│           ├── customer_order_confirmation.html
│           ├── customer_order_confirmation.txt
│           └── order_status_update.txt
└── management/
    └── commands/
        └── test_order_emails.py
```

## 🔍 Monitoring and Logging

The system includes comprehensive logging:

```python
import logging
logger = logging.getLogger(__name__)

# Logs successful notifications
logger.info(f"Admin notification email sent for order: {order.order_number}")

# Logs errors
logger.error(f"Failed to send admin notification email: {e}")
```

## 🚀 Usage Examples

### Manual Notification Sending
```python
from orders.utils import send_order_notification_email, send_order_confirmation_email
from orders.models import Order

order = Order.objects.get(order_number="ORD-12345")

# Send admin notification
send_order_notification_email(order)

# Send customer confirmation
send_order_confirmation_email(order)
```

### Using Notification Manager
```python
from orders.notifications import order_notification_manager

# Send all order creation notifications
notifications_sent = order_notification_manager.notify_order_created(order)

# Send status change notifications
notifications_sent = order_notification_manager.notify_order_status_changed(
    order, "Processing", "Shipped"
)
```

## 🔧 Customization

### Adding New Email Templates
1. Create new template files in `orders/templates/orders/emails/`
2. Add utility function in `orders/utils.py`
3. Update notification manager in `orders/notifications.py`

### Extending Notifications
- Add new notification types in `OrderNotificationManager`
- Create additional Django signals for other events
- Implement in-app notification models for persistent notifications

## 🛠️ Troubleshooting

### Common Issues

1. **Emails not sending**
   - Check email backend configuration
   - Verify SMTP credentials
   - Check Django logs for errors

2. **Templates not found**
   - Ensure template files are in correct directory
   - Check Django template settings

3. **No admin emails configured**
   - Create staff users with email addresses
   - Set ADMIN_EMAIL environment variable

### Debug Mode
Enable debug logging to see detailed notification information:

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'orders': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}
```

## 🎯 Next Steps

- Implement SMS notifications for order updates
- Add push notifications for mobile apps
- Create notification preferences for customers
- Build admin dashboard for notification management
- Add email templates for other order events (cancellation, refunds, etc.)
