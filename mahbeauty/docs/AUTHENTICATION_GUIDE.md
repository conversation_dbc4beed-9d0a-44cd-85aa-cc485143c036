# Authentication Guide - Email/Password + Google OAuth

Your Django application now supports both **Email/Password** and **Google OAuth** authentication using Djoser and Django REST Framework.

## 🔐 Available Authentication Methods

### 1. Email/Password Authentication
- User registration with email and password
- Login with JWT tokens
- Password reset functionality
- User profile management

### 2. Google OAuth Authentication
- Login with Google account
- Automatic user creation
- JWT token generation

## 📡 API Endpoints

### Email/Password Authentication

#### User Registration
```http
POST /auth/users/
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "securepassword123",
    "re_password": "securepassword123",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>"
}
```

**Response (201 Created):**
```json
{
    "email": "<EMAIL>",
    "id": 1
}
```

#### User Login
```http
POST /auth/jwt/create/
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "securepassword123"
}
```

**Response (200 OK):**
```json
{
    "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### Get User Profile
```http
GET /auth/users/me/
Authorization: Bearer <access_token>
```

**Response (200 OK):**
```json
{
    "id": 1,
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe"
}
```

#### Token Refresh
```http
POST /auth/jwt/refresh/
Content-Type: application/json

{
    "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### Password Reset
```http
POST /auth/users/reset_password/
Content-Type: application/json

{
    "email": "<EMAIL>"
}
```

### Google OAuth Authentication

#### Google Login
```http
POST /auth/o/google-oauth2/
Content-Type: application/json

{
    "access_token": "<google_access_token>"
}
```

## 🛠️ Frontend Integration Examples

### JavaScript/React Example

```javascript
// Registration
const register = async (userData) => {
    const response = await fetch('/auth/users/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData)
    });
    return response.json();
};

// Login
const login = async (email, password) => {
    const response = await fetch('/auth/jwt/create/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password })
    });
    const data = await response.json();
    
    if (response.ok) {
        // Store tokens
        localStorage.setItem('access_token', data.access);
        localStorage.setItem('refresh_token', data.refresh);
    }
    
    return data;
};

// Authenticated API calls
const fetchUserProfile = async () => {
    const token = localStorage.getItem('access_token');
    const response = await fetch('/auth/users/me/', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });
    return response.json();
};
```

### Python/Requests Example

```python
import requests

BASE_URL = "http://localhost:8000"

# Register user
def register_user(email, password, first_name, last_name):
    data = {
        "email": email,
        "password": password,
        "re_password": password,
        "first_name": first_name,
        "last_name": last_name
    }
    response = requests.post(f"{BASE_URL}/auth/users/", json=data)
    return response.json()

# Login user
def login_user(email, password):
    data = {"email": email, "password": password}
    response = requests.post(f"{BASE_URL}/auth/jwt/create/", json=data)
    return response.json()

# Get user profile
def get_user_profile(access_token):
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(f"{BASE_URL}/auth/users/me/", headers=headers)
    return response.json()
```

## 🔧 Configuration

### Current Settings
- **Email activation**: Disabled (for development)
- **Password confirmation**: Required
- **JWT tokens**: Enabled
- **Google OAuth**: Configured
- **CORS**: Enabled for development

### Environment Variables Required
```bash
# Google OAuth (optional)
SOCIAL_AUTH_GOOGLE_OAUTH2_KEY=your_google_client_id
SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET=your_google_client_secret

# Email settings (for production)
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password
```

## 🚀 Next Steps

1. **Enable Email Verification** (for production):
   - Configure email backend in settings
   - Set `SEND_ACTIVATION_EMAIL = True` in DJOSER settings

2. **Add Frontend Forms**:
   - Create registration/login forms
   - Implement Google OAuth button
   - Add token management

3. **Security Enhancements**:
   - Add rate limiting
   - Implement proper CORS settings
   - Add password strength validation

## ✅ Testing

Run the test script to verify everything works:
```bash
python test_auth_endpoints.py
```

All authentication endpoints are now fully functional! 🎉
