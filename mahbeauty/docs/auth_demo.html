<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MahBeauty Authentication Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"], input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .google-btn {
            background-color: #db4437;
        }
        .google-btn:hover {
            background-color: #c23321;
        }
        .success {
            color: green;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            color: red;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .hidden {
            display: none;
        }
        .user-info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🌸 MahBeauty Authentication Demo</h1>
    
    <!-- Registration Form -->
    <div class="container">
        <h2>📝 Register New Account</h2>
        <form id="registerForm">
            <div class="form-group">
                <label for="regEmail">Email:</label>
                <input type="email" id="regEmail" required>
            </div>
            <div class="form-group">
                <label for="regFirstName">First Name:</label>
                <input type="text" id="regFirstName" required>
            </div>
            <div class="form-group">
                <label for="regLastName">Last Name:</label>
                <input type="text" id="regLastName" required>
            </div>
            <div class="form-group">
                <label for="regPassword">Password:</label>
                <input type="password" id="regPassword" required>
            </div>
            <div class="form-group">
                <label for="regPasswordConfirm">Confirm Password:</label>
                <input type="password" id="regPasswordConfirm" required>
            </div>
            <button type="submit">Register</button>
        </form>
        <div id="registerResult"></div>
    </div>

    <!-- Login Form -->
    <div class="container">
        <h2>🔐 Login</h2>
        <form id="loginForm">
            <div class="form-group">
                <label for="loginEmail">Email:</label>
                <input type="email" id="loginEmail" required>
            </div>
            <div class="form-group">
                <label for="loginPassword">Password:</label>
                <input type="password" id="loginPassword" required>
            </div>
            <button type="submit">Login</button>
            <button type="button" class="google-btn" onclick="loginWithGoogle()">Login with Google</button>
        </form>
        <div id="loginResult"></div>
    </div>

    <!-- User Profile (shown after login) -->
    <div class="container hidden" id="userProfile">
        <h2>👤 User Profile</h2>
        <div id="profileInfo" class="user-info"></div>
        <button onclick="logout()">Logout</button>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';

        // Register form handler
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('regEmail').value;
            const firstName = document.getElementById('regFirstName').value;
            const lastName = document.getElementById('regLastName').value;
            const password = document.getElementById('regPassword').value;
            const passwordConfirm = document.getElementById('regPasswordConfirm').value;
            
            if (password !== passwordConfirm) {
                showResult('registerResult', 'Passwords do not match!', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/auth/users/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email,
                        first_name: firstName,
                        last_name: lastName,
                        password,
                        re_password: passwordConfirm
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult('registerResult', `Registration successful! User ID: ${data.id}`, 'success');
                    document.getElementById('registerForm').reset();
                } else {
                    showResult('registerResult', `Registration failed: ${JSON.stringify(data)}`, 'error');
                }
            } catch (error) {
                showResult('registerResult', `Error: ${error.message}`, 'error');
            }
        });

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            try {
                const response = await fetch(`${API_BASE}/auth/jwt/create/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();
                
                if (response.ok) {
                    localStorage.setItem('access_token', data.access);
                    localStorage.setItem('refresh_token', data.refresh);
                    showResult('loginResult', 'Login successful!', 'success');
                    document.getElementById('loginForm').reset();
                    await loadUserProfile();
                } else {
                    showResult('loginResult', `Login failed: ${data.detail || JSON.stringify(data)}`, 'error');
                }
            } catch (error) {
                showResult('loginResult', `Error: ${error.message}`, 'error');
            }
        });

        // Load user profile
        async function loadUserProfile() {
            const token = localStorage.getItem('access_token');
            if (!token) return;

            try {
                const response = await fetch(`${API_BASE}/auth/users/me/`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const user = await response.json();
                    document.getElementById('profileInfo').innerHTML = `
                        <strong>ID:</strong> ${user.id}<br>
                        <strong>Email:</strong> ${user.email}<br>
                        <strong>Name:</strong> ${user.first_name} ${user.last_name}<br>
                        <strong>Access Token:</strong> ${token.substring(0, 50)}...
                    `;
                    document.getElementById('userProfile').classList.remove('hidden');
                } else {
                    logout();
                }
            } catch (error) {
                console.error('Error loading profile:', error);
                logout();
            }
        }

        // Logout function
        function logout() {
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            document.getElementById('userProfile').classList.add('hidden');
            showResult('loginResult', 'Logged out successfully', 'success');
        }

        // Google login placeholder
        function loginWithGoogle() {
            alert('Google OAuth integration requires additional setup. Check the documentation for implementation details.');
        }

        // Utility function to show results
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
            setTimeout(() => {
                element.innerHTML = '';
            }, 5000);
        }

        // Check if user is already logged in on page load
        window.addEventListener('load', () => {
            if (localStorage.getItem('access_token')) {
                loadUserProfile();
            }
        });
    </script>
</body>
</html>
