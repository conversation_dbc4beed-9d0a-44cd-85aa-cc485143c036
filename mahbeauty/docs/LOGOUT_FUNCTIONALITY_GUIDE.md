# JWT Token Blacklist & Logout Functionality Guide

Your Django application now supports **secure logout functionality** using JWT token blacklisting with `rest_framework_simplejwt.token_blacklist`.

## 🔐 How It Works

### Token Lifecycle
1. **Login**: User receives `access_token` and `refresh_token`
2. **API Access**: Use `access_token` for authenticated requests
3. **Token Refresh**: Use `refresh_token` to get new `access_token`
4. **Logout**: Blacklist the `refresh_token` to invalidate it
5. **Security**: Blacklisted tokens cannot be used for refresh

### Token Rotation & Security
- **Token Rotation**: New refresh tokens are issued on each refresh
- **Automatic Blacklisting**: Old refresh tokens are blacklisted after rotation
- **Expiration**: Access tokens expire in 60 minutes, refresh tokens in 7 days

## 📡 API Endpoints

### Login (Get Tokens)
```http
POST /api/auth/jwt/create/
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password123"
}
```

**Response:**
```json
{
    "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Refresh Token
```http
POST /api/auth/jwt/refresh/
Content-Type: application/json

{
    "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**
```json
{
    "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Logout (Blacklist Token)
```http
POST /api/auth/jwt/blacklist/
Content-Type: application/json

{
    "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**
```json
{}
```
*Status: 200 OK (successful logout)*

## 🛠️ Frontend Integration

### JavaScript/React Example

```javascript
class AuthService {
    constructor() {
        this.baseURL = '/api/auth';
    }

    // Login and store tokens
    async login(email, password) {
        const response = await fetch(`${this.baseURL}/jwt/create/`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password })
        });
        
        if (response.ok) {
            const tokens = await response.json();
            localStorage.setItem('access_token', tokens.access);
            localStorage.setItem('refresh_token', tokens.refresh);
            return tokens;
        }
        throw new Error('Login failed');
    }

    // Refresh access token
    async refreshToken() {
        const refreshToken = localStorage.getItem('refresh_token');
        if (!refreshToken) throw new Error('No refresh token');

        const response = await fetch(`${this.baseURL}/jwt/refresh/`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ refresh: refreshToken })
        });

        if (response.ok) {
            const tokens = await response.json();
            localStorage.setItem('access_token', tokens.access);
            if (tokens.refresh) {
                localStorage.setItem('refresh_token', tokens.refresh);
            }
            return tokens;
        }
        throw new Error('Token refresh failed');
    }

    // Logout and blacklist token
    async logout() {
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
            try {
                await fetch(`${this.baseURL}/jwt/blacklist/`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ refresh: refreshToken })
                });
            } catch (error) {
                console.warn('Token blacklist failed:', error);
            }
        }
        
        // Clear local storage regardless of blacklist success
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
    }

    // Get current access token
    getAccessToken() {
        return localStorage.getItem('access_token');
    }

    // Check if user is logged in
    isLoggedIn() {
        return !!this.getAccessToken();
    }

    // Make authenticated API calls
    async authenticatedFetch(url, options = {}) {
        const token = this.getAccessToken();
        const headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            ...options.headers
        };

        let response = await fetch(url, { ...options, headers });

        // If token expired, try to refresh
        if (response.status === 401) {
            try {
                await this.refreshToken();
                const newToken = this.getAccessToken();
                headers['Authorization'] = `Bearer ${newToken}`;
                response = await fetch(url, { ...options, headers });
            } catch (error) {
                // Refresh failed, redirect to login
                this.logout();
                window.location.href = '/login';
            }
        }

        return response;
    }
}

// Usage example
const auth = new AuthService();

// Login
auth.login('<EMAIL>', 'password123')
    .then(() => console.log('Logged in successfully'))
    .catch(error => console.error('Login failed:', error));

// Logout
auth.logout()
    .then(() => console.log('Logged out successfully'));

// Make authenticated API call
auth.authenticatedFetch('/api/account/users/me/')
    .then(response => response.json())
    .then(profile => console.log('User profile:', profile));
```

## 🔧 Configuration Details

### JWT Settings (in settings.py)
```python
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': True,
}
```

### Key Features
- **Access Token**: 60-minute lifetime
- **Refresh Token**: 7-day lifetime  
- **Token Rotation**: New refresh token on each refresh
- **Auto Blacklisting**: Old tokens blacklisted automatically
- **Last Login Update**: Updates user's last_login on token creation

## 🛡️ Security Benefits

1. **Token Invalidation**: Logout immediately invalidates refresh tokens
2. **Rotation Protection**: Old tokens can't be reused after refresh
3. **Expiration Control**: Short-lived access tokens limit exposure
4. **Audit Trail**: Blacklisted tokens are tracked in database
5. **Session Management**: Proper logout prevents token reuse

## 📊 Admin Panel Management

The Django admin now includes **Token Blacklist** section with:

- **Blacklisted Tokens**: View all blacklisted refresh tokens
- **Outstanding Tokens**: View all active refresh tokens
- **Token Management**: Monitor and manage user sessions

Access via: `/admin/` → **Token Blacklist** section

## ✅ Testing

Run the test script to verify functionality:
```bash
python test_logout_functionality.py
```

The test verifies:
- ✅ User login and token generation
- ✅ Protected endpoint access with tokens
- ✅ Token refresh functionality
- ✅ Logout and token blacklisting
- ✅ Blacklisted token rejection

## 🚀 Production Considerations

1. **Token Cleanup**: Periodically clean expired blacklisted tokens
2. **Rate Limiting**: Add rate limiting to auth endpoints
3. **HTTPS Only**: Ensure tokens are only sent over HTTPS
4. **Secure Storage**: Use secure storage for tokens in frontend
5. **Monitoring**: Monitor failed authentication attempts

Your logout functionality is now fully implemented and secure! 🎉
