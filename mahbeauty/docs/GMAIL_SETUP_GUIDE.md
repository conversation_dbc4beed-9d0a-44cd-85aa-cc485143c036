# Gmail SMTP Setup Guide for MahBeauty

## 🚨 Current Issue
Your email configuration is now correctly loaded, but <PERSON><PERSON> is rejecting the authentication. This is because Gmail requires **App Passwords** for SMTP access when 2-Factor Authentication is enabled.

## 📧 Current Configuration Status
✅ Environment variables are loading correctly  
✅ EMAIL_BACKEND is set to SMTP  
✅ Email credentials are being read from .env  
❌ Gmail authentication is failing  

## 🔧 How to Fix Gmail Authentication

### Step 1: Enable 2-Factor Authentication
1. Go to your Google Account settings: https://myaccount.google.com/
2. Click on "Security" in the left sidebar
3. Under "Signing in to Google", click on "2-Step Verification"
4. Follow the setup process to enable 2FA

### Step 2: Generate App Password
1. After enabling 2FA, go back to Security settings
2. Under "Signing in to Google", click on "App passwords"
3. Select "Mail" as the app and "Other (Custom name)" as the device
4. Enter "MahBeauty Django App" as the custom name
5. Click "Generate"
6. **Copy the 16-character app password** (it will look like: `abcd efgh ijkl mnop`)

### Step 3: Update Your .env File
Replace your current EMAIL_HOST_PASSWORD with the app password:

```bash
# Use the 16-character app password (without spaces)
EMAIL_HOST_PASSWORD="abcdefghijklmnop"
```

### Step 4: Verify Email Address
Make sure the EMAIL_HOST_USER in your .env file is the exact Gmail address you used to generate the app password.

## 🧪 Testing After Setup

After updating your .env file with the app password, run the test again:

```bash
source .venv/bin/activate
python debug_email.py
```

You should see:
- ✅ Simple email: PASS
- ✅ HTML email: PASS  
- ✅ Order notification: PASS

## 🔍 Alternative: Check Current Credentials

If you believe your current credentials should work, verify:

1. **Email address**: Is `<EMAIL>` the correct Gmail address?
2. **Password**: Is the password in .env the app password or regular password?
3. **2FA**: Is 2-Factor Authentication enabled on the Gmail account?

## 🚀 Once Fixed

After Gmail authentication is working, your order notification system will:

1. ✅ Send admin notification emails to: `<EMAIL>`
2. ✅ Send customer confirmation emails
3. ✅ Send order status update emails
4. ✅ All emails will be sent via Gmail SMTP

## 📝 Security Notes

- Never commit your .env file to version control
- App passwords are safer than regular passwords for applications
- You can revoke app passwords anytime from Google Account settings
- Consider using environment variables in production instead of .env files

## 🆘 Still Having Issues?

If you continue to have problems:

1. Double-check the Gmail address is correct
2. Ensure you're using the app password, not regular password
3. Verify 2FA is enabled on the Gmail account
4. Try generating a new app password
5. Check if the Gmail account has any security restrictions
