from django.core.management.base import BaseCommand
from products.models import ProductCategory, Product


class Command(BaseCommand):
    help = 'Populate EAN category codes and generate EAN-13 codes for existing products'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting EAN code population...'))
        
        # Define category codes
        category_codes = {
            'Skincare': '001',
            'Makeup': '002', 
            'Hair Care': '003',
            'Fragrance': '004',
            'Body Care': '005',
            'Tools & Accessories': '006',
            'Men\'s Grooming': '007',
            'Wellness': '008',
            'Gift Sets': '009',
        }
        
        # Update categories with EAN codes
        categories_updated = 0
        for category in ProductCategory.objects.all():
            if not category.ean_category_code:
                if category.name in category_codes:
                    category.ean_category_code = category_codes[category.name]
                    category.save()
                    categories_updated += 1
                    self.stdout.write(f'Updated category "{category.name}" with code {category.ean_category_code}')
                else:
                    # Generate a sequential code for unknown categories
                    existing_codes = set(ProductCategory.objects.exclude(ean_category_code__isnull=True).values_list('ean_category_code', flat=True))
                    for i in range(10, 100):  # Start from 010 to avoid conflicts
                        code = f'{i:03d}'
                        if code not in existing_codes:
                            category.ean_category_code = code
                            category.save()
                            categories_updated += 1
                            self.stdout.write(f'Generated code {code} for category "{category.name}"')
                            break
        
        # Generate EAN-13 codes for products
        products_updated = 0
        for product in Product.objects.filter(ean_13_code__isnull=True):
            if product.category and product.category.ean_category_code:
                ean_code = product.generate_ean13_code()
                if ean_code:
                    product.ean_13_code = ean_code
                    product.save(update_fields=['ean_13_code'])
                    products_updated += 1
                    self.stdout.write(f'Generated EAN-13 {ean_code} for product "{product.name}"')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully updated {categories_updated} categories and {products_updated} products with EAN codes'
            )
        )
