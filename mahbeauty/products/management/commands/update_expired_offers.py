from django.core.management.base import BaseCommand
from django.utils import timezone
from products.models import Product
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Update expired product offers and manage offer lifecycle - Perfect for cron jobs"

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )
        parser.add_argument(
            '--notify-expiring',
            type=int,
            default=24,
            help='Hours before expiration to notify (default: 24)',
        )
        parser.add_argument(
            '--cleanup-old',
            action='store_true',
            help='Clean up offers that expired more than 30 days ago',
        )
        parser.add_argument(
            '--quiet',
            action='store_true',
            help='Suppress output (useful for cron jobs)',
        )

    def handle(self, *args, **options):
        now = timezone.now()
        dry_run = options['dry_run']
        notify_hours = options['notify_expiring']
        cleanup_old = options['cleanup_old']
        quiet = options['quiet']

        def log_message(message, style=None):
            """Helper to log messages based on quiet flag"""
            if not quiet:
                if style:
                    self.stdout.write(style(message))
                else:
                    self.stdout.write(message)
            logger.info(message)

        # Find expired offers
        expired_offers = Product.objects.filter(
            is_on_offer=True,
            offer_is_active=True,
            offer_end_date__lte=now
        )

        # Find offers expiring soon
        expiring_soon = Product.objects.filter(
            is_on_offer=True,
            offer_is_active=True,
            offer_end_date__gt=now,
            offer_end_date__lte=now + timedelta(hours=notify_hours)
        )

        if dry_run:
            log_message(f"DRY RUN: Would deactivate {expired_offers.count()} expired offers", self.style.WARNING)
            log_message(f"DRY RUN: {expiring_soon.count()} offers expiring in {notify_hours} hours", self.style.WARNING)

            for product in expired_offers:
                log_message(f"  - {product.name} (expired: {product.offer_end_date})")

            for product in expiring_soon:
                log_message(f"  - {product.name} (expires: {product.offer_end_date})")

            if cleanup_old:
                old_expired = Product.objects.filter(
                    is_on_offer=True,
                    offer_is_active=False,
                    offer_end_date__lt=now - timedelta(days=30)
                )
                log_message(f"DRY RUN: Would clean up {old_expired.count()} old expired offers", self.style.WARNING)
        else:
            # Deactivate expired offers
            count = expired_offers.update(offer_is_active=False)

            if count > 0:
                log_message(f"{count} expired offer(s) deactivated.", self.style.SUCCESS)

                # Log each deactivated offer for audit trail
                for product in Product.objects.filter(
                    is_on_offer=True,
                    offer_is_active=False,
                    offer_end_date__lte=now
                ):
                    logger.info(f"Deactivated expired offer for: {product.name}")

            # Log expiring offers
            if expiring_soon.exists():
                log_message(f"{expiring_soon.count()} offer(s) expiring in {notify_hours} hours:", self.style.WARNING)
                for product in expiring_soon:
                    log_message(f"  - {product.name} (expires: {product.offer_end_date})")
                    logger.warning(f"Offer expiring soon for: {product.name} at {product.offer_end_date}")

            # Clean up old expired offers if requested
            if cleanup_old:
                old_expired = Product.objects.filter(
                    is_on_offer=True,
                    offer_is_active=False,
                    offer_end_date__lt=now - timedelta(days=30)
                )

                cleanup_count = 0
                for product in old_expired:
                    product.is_on_offer = False
                    product.offer_price = None
                    product.offer_start_date = None
                    product.offer_end_date = None
                    product.offer_is_active = False
                    product.save()
                    cleanup_count += 1
                    logger.info(f"Cleaned up old expired offer for: {product.name}")

                if cleanup_count > 0:
                    log_message(f"Cleaned up {cleanup_count} old expired offers", self.style.SUCCESS)

            # Summary statistics
            active_offers = Product.objects.filter(
                is_on_offer=True,
                offer_is_active=True,
                offer_end_date__gt=now
            ).count()

            total_on_offer = Product.objects.filter(is_on_offer=True).count()

            log_message(f"Summary: {active_offers} active offers, {total_on_offer} total products on offer", self.style.SUCCESS)

            # Exit with appropriate code for cron job monitoring
            if count > 0 or expiring_soon.exists():
                return 0  # Success with activity
            else:
                return 0  # Success with no activity
