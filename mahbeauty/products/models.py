from datetime import <PERSON><PERSON><PERSON>

from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django.db.models import Avg
from django.urls import reverse
from django.utils import timezone
from django_jsonform.models.fields import <PERSON><PERSON><PERSON><PERSON>

from account.models import UserAccount

# Create your models here.


class ProductCategory(models.Model):
    name = models.CharField(max_length=50, unique=True)
    ean_category_code = models.CharField(
        max_length=3,
        unique=True,
        help_text="3-digit EAN category code (e.g., 001, 002, 003)",
        blank=True,
        null=True
    )

    # to make get link of the product according to the category

    def get_url(self):
        return reverse("product_by_category", args=[self.id])

    def __str__(self):
        return self.name


    class Meta:
        db_table = "product_categories"
        ordering = ["name"]
        verbose_name = "Product Category"
        verbose_name_plural = "Product Categories"
        indexes = [
            models.Index(fields=["name"], name="idx_product_category_name"),
        ]


class Product(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    price = models.DecimalField(
        max_digits=10, decimal_places=2, validators=[MinValueValidator(0)]
    )

    category = models.ForeignKey(
        ProductCategory, on_delete=models.CASCADE, related_name="products"
    )
    rating = models.FloatField(
        validators=[MinValueValidator(0), MaxValueValidator(5)], default=0.0
    )
    in_stock = models.BooleanField(default=True)
    ingredients = JSONField(
        schema={
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "quantity": {"type": "string"},
                },
                "required": ["name", "quantity"],
            },
        },
        blank=True,
        null=True,
    )
    usage_instructions = models.TextField(blank=True, null=True)
    benefits = JSONField(
        schema={"type": "array", "items": {"type": "string"}}, blank=True, null=True
    )
    image = models.ImageField(upload_to="products/", blank=True, null=True)
    ean_13_code = models.CharField(
        max_length=13,
        unique=True,
        help_text="13-digit EAN-13 barcode (auto-generated from category + product ID)",
        blank=True,
        null=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    # Offer-related fields
    is_on_offer = models.BooleanField(
        default=False, help_text="Enable to add this product to offers"
    )
    offer_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        blank=True,
        null=True,
        help_text="Discounted price when on offer",
    )
    offer_start_date = models.DateTimeField(
        blank=True, null=True, help_text="When the offer starts"
    )
    offer_end_date = models.DateTimeField(
        blank=True, null=True, help_text="When the offer ends"
    )
    offer_is_active = models.BooleanField(
        default=True, help_text="Whether the offer is currently active"
    )

    def generate_ean13_code(self):
        """Generate EAN-13 code from category code and product ID"""
        if not self.category.ean_category_code:
            return None

        # Format: Country(2) + Company(5) + Product(5) + Check(1)
        # Using: 99 (internal) + category(3) + product_id(6) + check(1)
        country_company = "99"  # Internal use country code
        category_code = self.category.ean_category_code.zfill(3)
        product_code = str(self.id).zfill(6)

        # Calculate check digit using EAN-13 algorithm
        code_without_check = country_company + category_code + product_code
        odd_sum = sum(int(code_without_check[i]) for i in range(0, 11, 2))
        even_sum = sum(int(code_without_check[i]) for i in range(1, 11, 2))
        total = odd_sum + (even_sum * 3)
        check_digit = (10 - (total % 10)) % 10

        return code_without_check + str(check_digit)

    def save(self, *args, **kwargs):
        # Generate EAN-13 code if not exists and category has code
        if not self.ean_13_code and self.category and self.category.ean_category_code:
            super().save(*args, **kwargs)  # Save first to get ID
            self.ean_13_code = self.generate_ean13_code()
            super().save(update_fields=['ean_13_code'])
        else:
            super().save(*args, **kwargs)

    class Meta:
        db_table = "products"
        ordering = ["-created_at"]
        verbose_name = "Product Details"
        verbose_name_plural = "Products Details"
        indexes = [
            models.Index(fields=["name"], name="idx_product_name"),
            models.Index(fields=["category"], name="idx_product_category"),
            models.Index(fields=["rating"], name="idx_product_rating"),
            models.Index(fields=["price"], name="idx_product_price"),
            models.Index(fields=["in_stock"], name="idx_product_in_stock"),
            models.Index(fields=["is_on_offer"], name="idx_product_is_on_offer"),
            models.Index(fields=["offer_end_date"], name="idx_product_offer_end_date"),
        ]

    def save(self, *args, **kwargs):
        """Override save to handle offer logic"""
        now = timezone.now()

        # Auto-set offer start date if not provided and product is on offer
        if self.is_on_offer and not self.offer_start_date:
            self.offer_start_date = now

        # Auto-set offer end date if not provided (default 7 days)
        if self.is_on_offer and not self.offer_end_date:
            self.offer_end_date = now + timedelta(days=7)

        # Auto-deactivate offer if expired
        if self.offer_end_date and self.offer_end_date <= now:
            self.offer_is_active = False

        # Ensure offer price is set if product is on offer
        if self.is_on_offer and not self.offer_price:
            self.offer_price = self.price * 0.9  # Default 10% discount

        super().save(*args, **kwargs)

    @classmethod
    def deactivate_expired_offers(cls):
        """Class method to deactivate all expired offers"""
        now = timezone.now()
        expired_count = cls.objects.filter(
            is_on_offer=True, offer_is_active=True, offer_end_date__lte=now
        ).update(offer_is_active=False)
        return expired_count

    def __str__(self):
        return self.name

    # to get the specific product url inside the specific category

    def get_url(self):
        # Note: This method needs to be updated when you add slug fields to models
        # For now, using IDs as placeholders
        return reverse("product_detail", args=[self.category.id, self.id])

    # calculating the average rating of the product

    def averageRating(self):
        reviews = ReviewRating.objects.filter(product=self).aggregate(
            average=Avg("rating")
        )
        avg = 0
        if reviews["average"] is not None:
            avg = float(reviews["average"])
        return avg

    def reveiewCount(self):
        reviews = ReviewRating.objects.filter(product=self)
        return reviews.count()

    # Offer-related methods
    def is_offer_valid(self):
        """Check if the current offer is valid"""
        if not self.is_on_offer or not self.offer_is_active:
            return False

        now = timezone.now()
        if self.offer_start_date and self.offer_start_date > now:
            return False
        if self.offer_end_date and self.offer_end_date <= now:
            return False
        return True

    def get_current_price(self):
        """Get the current price (offer price if valid, otherwise regular price)"""
        if self.is_offer_valid() and self.offer_price:
            return self.offer_price
        return self.price

    def get_discount_percentage(self):
        """Calculate discount percentage"""
        if not self.is_offer_valid() or not self.offer_price:
            return 0
        if self.price <= 0:
            return 0
        return round(((self.price - self.offer_price) / self.price) * 100, 2)

    def get_savings_amount(self):
        """Calculate savings amount"""
        if not self.is_offer_valid() or not self.offer_price:
            return 0
        return self.price - self.offer_price
    



class VariationManager(models.Manager):
    def color(self):
        return super(VariationManager, self).filter(
            variation_category="color", is_active=True
        )


variation_category_choices = (("color", "color"),)


class Variation(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name="variations")
    variation_category = models.CharField(
        max_length=100, choices=variation_category_choices
    )
    variation_value = models.CharField(max_length=100)
    is_active = models.BooleanField(default=True)
    created_date = models.DateTimeField(auto_now=True)

    objects = VariationManager()

    def __str__(self):
        return f"{self.product.name} - {self.variation_category}: {self.variation_value}"

    class Meta:
        verbose_name = "Product Variation"
        verbose_name_plural = "Product Variations"
        ordering = ["variation_category", "variation_value"]


class ReviewRating(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    user = models.ForeignKey(UserAccount, on_delete=models.CASCADE)
    subject = models.CharField(max_length=100, blank=True)
    review = models.TextField(blank=True)
    rating = models.FloatField()
    ip = models.CharField(max_length=20, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.subject

    class Meta:
        verbose_name = "Product Review & Rating"
        verbose_name_plural = "Products Reviews & Ratings"
