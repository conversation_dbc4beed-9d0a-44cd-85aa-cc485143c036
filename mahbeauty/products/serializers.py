from rest_framework import serializers
from .models import ProductCategory, Product, Variation, ReviewRating
from account.models import UserAccount


class ProductCategorySerializer(serializers.ModelSerializer):
    """Serializer for ProductCategory model"""
    product_count = serializers.SerializerMethodField()
    average_rating = serializers.SerializerMethodField()
    
    class Meta:
        model = ProductCategory
        fields = ['id', 'name', 'product_count', 'average_rating', 'ean_category_code']
        read_only_fields = ['id']

    def get_product_count(self, obj):
        """Get count of active products in this category"""
        return obj.products.filter(is_active=True).count()

    def get_average_rating(self, obj):
        """Get average rating of products in this category"""
        products = obj.products.filter(is_active=True)
        if products.exists():
            total_rating = sum(product.rating for product in products)
            return round(total_rating / products.count(), 2)
        return 0.0

    def validate_name(self, value):
        """Validate category name uniqueness"""
        if ProductCategory.objects.filter(name__iexact=value).exists():
            if not self.instance or self.instance.name.lower() != value.lower():
                raise serializers.ValidationError("Category with this name already exists")
        return value


class ProductCategoryListSerializer(serializers.ModelSerializer):
    """Optimized serializer for ProductCategory list view"""
    product_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ProductCategory
        fields = ['id', 'name', 'product_count']

    def get_product_count(self, obj):
        """Get count of active products in this category"""
        return getattr(obj, '_product_count', obj.products.filter(is_active=True).count())


class VariationSerializer(serializers.ModelSerializer):
    """Serializer for Variation model"""
    variation_category_display = serializers.CharField(source='get_variation_category_display', read_only=True)
    
    class Meta:
        model = Variation
        fields = [
            'id', 'variation_category', 'variation_category_display', 
            'variation_value', 'is_active', 'created_date'
        ]
        read_only_fields = ['id', 'created_date']

    def validate(self, attrs):
        """Validate variation uniqueness for product"""
        variation_category = attrs.get('variation_category')
        variation_value = attrs.get('variation_value')
        
        if variation_category and variation_value:
            # For inline usage, the product context is handled by the parent
            # No need to validate uniqueness here as it's handled at the model level
            pass
        
        return attrs


class VariationCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating Variation"""
    
    class Meta:
        model = Variation
        fields = ['variation_category', 'variation_value', 'is_active']

    def validate(self, attrs):
        """Validate variation data"""
        variation_category = attrs.get('variation_category')
        variation_value = attrs.get('variation_value')
        
        if not variation_category or not variation_value:
            raise serializers.ValidationError("Both variation category and value are required")
        
        return attrs


class VariationListSerializer(serializers.ModelSerializer):
    """Optimized serializer for Variation list view"""
    variation_category_display = serializers.CharField(source='get_variation_category_display', read_only=True)
    
    class Meta:
        model = Variation
        fields = ['id', 'variation_category_display', 'variation_value', 'is_active']






# Nested serializers for related models
class UserBasicSerializer(serializers.ModelSerializer):
    """Basic user info for reviews"""
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    
    class Meta:
        model = UserAccount
        fields = ['id', 'email', 'first_name', 'last_name', 'full_name']


class ReviewRatingSerializer(serializers.ModelSerializer):
    """Serializer for ReviewRating model"""
    user = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = ReviewRating
        fields = [
            'id', 'product', 'user', 'subject', 'review',
            'rating', 'ip', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'ip']

    def validate_rating(self, value):
        """Validate rating is within valid range"""
        if value < 0 or value > 5:
            raise serializers.ValidationError("Rating must be between 0 and 5")
        return value

    def validate(self, attrs):
        """Validate user hasn't already reviewed this product"""
        product = attrs.get('product')
        user = self.context['request'].user if self.context.get('request') else None
        
        if product and user and user.is_authenticated:
            existing_review = ReviewRating.objects.filter(product=product, user=user)
            
            if self.instance:
                existing_review = existing_review.exclude(pk=self.instance.pk)
            
            if existing_review.exists():
                raise serializers.ValidationError(
                    "You have already reviewed this product"
                )
        
        return attrs


class ReviewRatingCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating ReviewRating"""
    product_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = ReviewRating
        fields = ['product_id', 'subject', 'review', 'rating']

    def validate_product_id(self, value):
        """Validate product exists"""
        try:
            Product.objects.get(id=value)
            return value
        except Product.DoesNotExist:
            raise serializers.ValidationError("Product does not exist")

    def create(self, validated_data):
        """Create review with product and user"""
        product_id = validated_data.pop('product_id')
        product = Product.objects.get(id=product_id)
        user = self.context['request'].user
        
        # Get client IP
        request = self.context.get('request')
        ip = request.META.get('REMOTE_ADDR', '') if request else ''
        
        return ReviewRating.objects.create(
            product=product,
            user=user,
            ip=ip,
            **validated_data
        )


class ReviewRatingListSerializer(serializers.ModelSerializer):
    """Optimized serializer for ReviewRating list view"""
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)

    class Meta:
        model = ReviewRating
        fields = ['id', 'user_name', 'subject', 'rating', 'created_at']


class ProductSerializer(serializers.ModelSerializer):
    """Serializer for Product model"""
    category = ProductCategorySerializer(read_only=True)
    variations = VariationSerializer(many=True, read_only=True)
    reviews = ReviewRatingSerializer(source='reviewrating_set', many=True, read_only=True)
    average_rating = serializers.SerializerMethodField()
    review_count = serializers.SerializerMethodField()
    current_price = serializers.SerializerMethodField()
    discount_percentage = serializers.SerializerMethodField()
    savings_amount = serializers.SerializerMethodField()
    is_offer_valid = serializers.SerializerMethodField()
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'description', 'price', 'category', 'rating',
            'in_stock', 'ingredients', 'usage_instructions', 'benefits',
            'image', 'created_at', 'updated_at', 'is_active', 'variations',
            'reviews', 'average_rating', 'review_count', 'ean_13_code',
            'is_on_offer', 'offer_price', 'offer_start_date', 'offer_end_date', 'offer_is_active',
            'current_price', 'discount_percentage', 'savings_amount', 'is_offer_valid'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'rating']

    def get_average_rating(self, obj):
        """Get calculated average rating"""
        return obj.averageRating()

    def get_review_count(self, obj):
        """Get count of approved reviews"""
        return obj.reveiewCount()

    def get_current_price(self, obj):
        """Get current effective price"""
        return obj.get_current_price()

    def get_discount_percentage(self, obj):
        """Get discount percentage if offer is valid"""
        return obj.get_discount_percentage()

    def get_savings_amount(self, obj):
        """Get savings amount if offer is valid"""
        return obj.get_savings_amount()

    def get_is_offer_valid(self, obj):
        """Check if offer is currently valid"""
        return obj.is_offer_valid()

    def validate_price(self, value):
        """Validate price is positive"""
        if value <= 0:
            raise serializers.ValidationError("Price must be greater than 0")
        return value

    def validate_offer_price(self, value):
        """Validate offer price"""
        if value is not None and value <= 0:
            raise serializers.ValidationError("Offer price must be greater than 0")
        return value

    def validate(self, attrs):
        """Cross-field validation"""
        price = attrs.get('price', getattr(self.instance, 'price', None))
        offer_price = attrs.get('offer_price')
        is_on_offer = attrs.get('is_on_offer', getattr(self.instance, 'is_on_offer', False))

        # If product is on offer, offer price must be set and less than regular price
        if is_on_offer:
            if not offer_price:
                raise serializers.ValidationError({
                    'offer_price': 'Offer price is required when product is on offer'
                })
            if price and offer_price >= price:
                raise serializers.ValidationError({
                    'offer_price': 'Offer price must be less than regular price'
                })

        return attrs

    def validate_rating(self, value):
        """Validate rating is within valid range"""
        if value < 0 or value > 5:
            raise serializers.ValidationError("Rating must be between 0 and 5")
        return value


class ProductCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating Product"""
    category_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = Product
        fields = [
            'name', 'description', 'price', 'category_id', 'in_stock',
            'ingredients', 'usage_instructions', 'benefits', 'image', 'is_active'
        ]

    def validate_category_id(self, value):
        """Validate category exists"""
        try:
            ProductCategory.objects.get(id=value)
            return value
        except ProductCategory.DoesNotExist:
            raise serializers.ValidationError("Category does not exist")

    def create(self, validated_data):
        """Create product with category"""
        category_id = validated_data.pop('category_id')
        category = ProductCategory.objects.get(id=category_id)
        return Product.objects.create(category=category, **validated_data)


class ProductListSerializer(serializers.ModelSerializer):
    """Optimized serializer for Product list view"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    variations = VariationSerializer(many=True, read_only=True)
    average_rating = serializers.SerializerMethodField()
    review_count = serializers.SerializerMethodField()
    has_offer = serializers.SerializerMethodField()
    discounted_price = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'price', 'category_name', 'rating', 'average_rating',
            'review_count', 'in_stock', 'image', 'is_active', 'has_offer',
            'discounted_price', 'created_at', 'variations', 'ean_13_code'
        ]

    def get_average_rating(self, obj):
        """Get calculated average rating"""
        return getattr(obj, '_avg_rating', obj.averageRating())

    def get_review_count(self, obj):
        """Get count of approved reviews"""
        return getattr(obj, '_review_count', obj.reveiewCount())

    def get_has_offer(self, obj):
        """Check if product has active offers"""
        return obj.is_on_offer and obj.is_offer_valid()

    def get_discounted_price(self, obj):
        """Get current discounted price if offer exists"""
        if obj.is_on_offer and obj.is_offer_valid():
            return obj.offer_price or obj.price
        return obj.price


class ProductBasicSerializer(serializers.ModelSerializer):
    """Basic product info for other serializers"""
    category_name = serializers.CharField(source='category.name', read_only=True)

    class Meta:
        model = Product
        fields = ['id', 'name', 'price', 'image', 'category_name', 'in_stock', 'is_active']
