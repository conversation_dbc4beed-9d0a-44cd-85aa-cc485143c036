# Products App API Documentation

This document describes the REST API endpoints for the products app, including serializers and viewsets for managing product categories, products, variations, offers, and reviews.

## 📋 Overview

The products app provides the following API endpoints:
- **Product Categories**: Product category management
- **Products**: Product catalog management
- **Variations**: Product variation management (colors, sizes, etc.)
- **Offers**: Product discount and promotion management
- **Reviews**: Product review and rating management

## 🔗 Base URL

All endpoints are prefixed with `/api/products/`

## 📊 Models & Endpoints

### 1. Product Categories API

**Base URL**: `/api/products/categories/`

#### Model Fields
- `id`: Auto-generated primary key
- `name`: Category name (string, max 50 chars, unique)

#### Endpoints

| Method | Endpoint | Description | Permission |
|--------|----------|-------------|------------|
| GET | `/api/products/categories/` | List categories | Read Only |
| POST | `/api/products/categories/` | Create new category | Authenticated |
| GET | `/api/products/categories/{id}/` | Retrieve specific category | Read Only |
| PUT | `/api/products/categories/{id}/` | Update category | Authenticated |
| PATCH | `/api/products/categories/{id}/` | Partial update category | Authenticated |
| DELETE | `/api/products/categories/{id}/` | Delete category | Authenticated |

#### Custom Actions

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/products/categories/{id}/products/` | Get products in category |
| GET | `/api/products/categories/with_products/` | Get categories with products |

### 2. Products API

**Base URL**: `/api/products/products/`

#### Model Fields
- `id`: Auto-generated primary key
- `name`: Product name (string, max 255 chars)
- `description`: Product description (text, optional)
- `price`: Product price (decimal, min 0)
- `category`: Foreign key to ProductCategory
- `rating`: Product rating (float, 0-5, auto-calculated)
- `in_stock`: Stock availability (boolean)
- `ingredients`: JSON array of ingredients
- `usage_instructions`: Usage instructions (text, optional)
- `benefits`: JSON array of benefits
- `image`: Product image (optional)
- `is_active`: Active status (boolean)

#### Endpoints

| Method | Endpoint | Description | Permission |
|--------|----------|-------------|------------|
| GET | `/api/products/products/` | List products | Read Only |
| POST | `/api/products/products/` | Create new product | Authenticated |
| GET | `/api/products/products/{id}/` | Retrieve specific product | Read Only |
| PUT | `/api/products/products/{id}/` | Update product | Authenticated |
| PATCH | `/api/products/products/{id}/` | Partial update product | Authenticated |
| DELETE | `/api/products/products/{id}/` | Delete product | Authenticated |

#### Custom Actions

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/products/products/{id}/variations/` | Get product variations |
| GET | `/api/products/products/{id}/offers/` | Get product offers |
| GET | `/api/products/products/{id}/reviews/` | Get product reviews |
| GET | `/api/products/products/{id}/current_offer/` | Get current active offer |
| GET | `/api/products/products/featured/` | Get featured products |
| GET | `/api/products/products/on_sale/` | Get products on sale |

### 3. Variations API

**Base URL**: `/api/products/variations/`

#### Model Fields
- `id`: Auto-generated primary key
- `product`: Foreign key to Product
- `variation_category`: Variation type (choices: color)
- `variation_value`: Variation value (string, max 100 chars)
- `is_active`: Active status (boolean)
- `created_date`: Creation timestamp

#### Endpoints

| Method | Endpoint | Description | Permission |
|--------|----------|-------------|------------|
| GET | `/api/products/variations/` | List variations | Read Only |
| POST | `/api/products/variations/` | Create new variation | Authenticated |
| GET | `/api/products/variations/{id}/` | Retrieve specific variation | Read Only |
| PUT | `/api/products/variations/{id}/` | Update variation | Authenticated |
| PATCH | `/api/products/variations/{id}/` | Partial update variation | Authenticated |
| DELETE | `/api/products/variations/{id}/` | Delete variation | Authenticated |

#### Custom Actions

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/products/variations/{id}/toggle_active/` | Toggle variation status |
| GET | `/api/products/variations/by_category/?category={cat}` | Get variations by category |

### 4. Offers API

**Base URL**: `/api/products/offers/`

#### Model Fields
- `id`: Auto-generated primary key
- `product`: Foreign key to Product
- `price`: Discount amount (integer, optional)
- `is_active`: Active status (boolean)
- `start_date`: Offer start date (auto-generated)
- `end_date`: Offer end date (optional, defaults to 1 day)

#### Endpoints

| Method | Endpoint | Description | Permission |
|--------|----------|-------------|------------|
| GET | `/api/products/offers/` | List offers | Read Only |
| POST | `/api/products/offers/` | Create new offer | Authenticated |
| GET | `/api/products/offers/{id}/` | Retrieve specific offer | Read Only |
| PUT | `/api/products/offers/{id}/` | Update offer | Authenticated |
| PATCH | `/api/products/offers/{id}/` | Partial update offer | Authenticated |
| DELETE | `/api/products/offers/{id}/` | Delete offer | Authenticated |

#### Custom Actions

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/products/offers/{id}/toggle_active/` | Toggle offer status |
| POST | `/api/products/offers/{id}/extend_offer/` | Extend offer duration |
| GET | `/api/products/offers/active/` | Get active offers |
| GET | `/api/products/offers/expired/` | Get expired offers |

### 5. Reviews API

**Base URL**: `/api/products/reviews/`

#### Model Fields
- `id`: Auto-generated primary key
- `product`: Foreign key to Product
- `user`: Foreign key to UserAccount
- `subject`: Review subject (string, max 100 chars, optional)
- `review`: Review text (text, optional)
- `rating`: Rating value (float, 0-5)
- `ip`: Client IP address (auto-captured)

#### Endpoints

| Method | Endpoint | Description | Permission |
|--------|----------|-------------|------------|
| GET | `/api/products/reviews/` | List reviews | Authenticated |
| POST | `/api/products/reviews/` | Create new review | Authenticated |
| GET | `/api/products/reviews/{id}/` | Retrieve specific review | Authenticated |
| PUT | `/api/products/reviews/{id}/` | Update review | Authenticated |
| PATCH | `/api/products/reviews/{id}/` | Partial update review | Authenticated |
| DELETE | `/api/products/reviews/{id}/` | Delete review | Authenticated |

#### Custom Actions

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/products/reviews/my_reviews/` | Get current user's reviews |
| GET | `/api/products/reviews/by_rating/?rating={rating}` | Get reviews by rating |
| GET | `/api/products/reviews/product_reviews/?product_id={id}` | Get reviews for product |

## 🔍 Filtering & Search

### Products Filtering
- **Search**: `name`, `description`, `category__name`
- **Filters**: `category`, `in_stock`, `is_active`
- **Ordering**: `name`, `price`, `rating`, `created_at`

### Reviews Filtering
- **Search**: `product__name`, `subject`, `review`, `user__email`
- **Filters**: `rating`, `product`, `user`
- **Ordering**: `created_at`, `rating`

### Variations Filtering
- **Search**: `product__name`, `variation_value`
- **Filters**: `variation_category`, `is_active`, `product`
- **Ordering**: `created_date`, `variation_category`, `variation_value`

### Offers Filtering
- **Search**: `product__name`
- **Filters**: `is_active`, `product`
- **Ordering**: `start_date`, `end_date`, `price`

## 🔐 Permissions

- **Read Operations**: Available to all users (authenticated or anonymous)
- **Write Operations**: Require authentication
- **Staff Operations**: Some actions (like review approval) require staff permissions

## 📝 Usage Examples

### Create a Product Category
```bash
POST /api/products/categories/
{
    "name": "Skincare"
}
```

### Create a Product
```bash
POST /api/products/products/
{
    "name": "Moisturizing Cream",
    "description": "Hydrating cream for all skin types",
    "price": "25.99",
    "category_id": 1,
    "in_stock": true,
    "ingredients": [
        {"name": "Hyaluronic Acid", "quantity": "2%"},
        {"name": "Vitamin E", "quantity": "1%"}
    ],
    "benefits": ["Hydrates skin", "Anti-aging", "Gentle formula"]
}
```

### Create a Product Variation
```bash
POST /api/products/variations/
{
    "product_id": 1,
    "variation_category": "color",
    "variation_value": "Light Pink",
    "is_active": true
}
```

### Create an Offer
```bash
POST /api/products/offers/
{
    "product_id": 1,
    "price": 500,
    "is_active": true,
    "end_date": "2024-12-31T23:59:59Z"
}
```

### Create a Review
```bash
POST /api/products/reviews/
{
    "product_id": 1,
    "subject": "Great product!",
    "review": "This cream works wonderfully for my skin.",
    "rating": 4.5
}
```

## 🚨 Error Handling

The API returns appropriate HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

Validation errors are returned in the following format:
```json
{
    "field_name": ["Error message"]
}
```
