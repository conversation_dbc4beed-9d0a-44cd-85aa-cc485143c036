import { useState, useEffect } from 'react';
import { getOrganizationInfo } from '@/api/apiService';

export interface OrganizationInfo {
  name: string;
  phone: string;
  email: string;
  location: string;
  logo: string;
  facebook: string;
  youtube: string;
  instagram: string;
  tiktok: string;
  mapping_url: string;
}

// Simple global cache
let cachedOrgInfo: OrganizationInfo | null = null;
let isFetching = false;

// Custom hook to use organization info
export const useOrganizationInfo = () => {
  const [orgInfo, setOrgInfo] = useState<OrganizationInfo | null>(cachedOrgInfo);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      // If we already have cached data, use it immediately
      if (cachedOrgInfo) {
        setOrgInfo(cachedOrgInfo);
        setLoading(false);
        setError(null);
        return;
      }

      // If another component is already fetching, wait
      if (isFetching) {
        setLoading(true);
        // Check periodically if data is available
        const checkInterval = setInterval(() => {
          if (cachedOrgInfo) {
            setOrgInfo(cachedOrgInfo);
            setLoading(false);
            setError(null);
            clearInterval(checkInterval);
          } else if (!isFetching) {
            // Fetching failed
            setLoading(false);
            clearInterval(checkInterval);
          }
        }, 100);
        return;
      }

      // Start fetching
      try {
        isFetching = true;
        setLoading(true);
        setError(null);

        const response = await getOrganizationInfo();

        if (response && response.success) {
          cachedOrgInfo = response.data;
          setOrgInfo(response.data);
          setError(null);
        } else {
          const errorMsg = 'Failed to fetch organization info';
          setError(errorMsg);
        }
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        setError(errorMsg);
      } finally {
        isFetching = false;
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const refetch = async () => {
    cachedOrgInfo = null;
    isFetching = false;
    setOrgInfo(null);
    setLoading(true);
    setError(null);

    try {
      isFetching = true;
      const response = await getOrganizationInfo();

      if (response && response.success) {
        cachedOrgInfo = response.data;
        setOrgInfo(response.data);
        setError(null);
      } else {
        setError('Failed to fetch organization info');
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMsg);
    } finally {
      isFetching = false;
      setLoading(false);
    }
  };

  return {
    orgInfo,
    loading,
    error,
    refetch
  };
};
