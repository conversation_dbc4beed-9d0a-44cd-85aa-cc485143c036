import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Star, ArrowDown, Sparkles, Phone } from 'lucide-react';
import { getHeroSlides } from '@/api/apiService';

interface HeroImage {
  id: number;
  image_url: string;
  image: string;
}

interface HeroSlide {
  id: number;
  title: string;
  description: string;
  client: number;
  service: number;
  experience: number;
  created_at: string;
  updated_at: string;
  images: HeroImage[];
}

const Hero = () => {
  const navigate = useNavigate();
  const [imageLoaded, setImageLoaded] = useState(false);
  const [currentImage, setCurrentImage] = useState(0);
  const [heroData, setHeroData] = useState<HeroSlide | null>(null);
  const [loading, setLoading] = useState(true);

  // Fallback data for when API is not available
  const fallbackData: HeroSlide = {
    id: 1,
    title: "Where Beauty Meets Luxury",
    description: "Indulge in premium beauty treatments crafted with precision and care. Experience the epitome of elegance at our exclusive beauty parlour.",
    client: 500,
    service: 50,
    experience: 5,
    created_at: "",
    updated_at: "",
    images: [
      {
        id: 1,
        image_url: 'https://images.unsplash.com/photo-1520472354-b33ff0c44a43?q=80&w=2126&auto=format&fit=crop',
        image: 'https://images.unsplash.com/photo-1520472354-b33ff0c44a43?q=80&w=2126&auto=format&fit=crop'
      },
      {
        id: 2,
        image_url: 'https://images.unsplash.com/photo-1522337320788-8b13dee7a37e?q=80&w=2069&auto=format&fit=crop',
        image: 'https://images.unsplash.com/photo-1522337320788-8b13dee7a37e?q=80&w=2069&auto=format&fit=crop'
      },
      {
        id: 3,
        image_url: 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?q=80&w=2069&auto=format&fit=crop',
        image: 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?q=80&w=2069&auto=format&fit=crop'
      }
    ]
  };

  useEffect(() => {
    const fetchHeroData = async () => {
      try {
        setLoading(true);
        const response = await getHeroSlides();

        if (response && response.success && response.data && response.data.length > 0) {
          setHeroData(response.data[0]);
        } else {
          setHeroData(fallbackData);
        }
      } catch (error) {
        setHeroData(fallbackData);
      } finally {
        setLoading(false);
      }
    };

    fetchHeroData();
  }, []);

  // Auto-rotate images every 5 seconds
  useEffect(() => {
    if (!heroData) return;
    
    const interval = setInterval(() => {
      setCurrentImage((prev) => (prev + 1) % heroData.images.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [heroData]);

  const currentHeroData = heroData || fallbackData;
  const heroImages = currentHeroData.images.map(img => img.image_url || img.image);

  if (loading) {
    return (
      <section className="min-h-screen relative overflow-hidden bg-gradient-to-br from-amber-50 to-rose-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-rose-200 mx-auto mb-4"></div>
          <div className="text-rose-700 font-medium">Loading your beauty experience...</div>
        </div>
      </section>
    );
  }

  return (
    <section className="min-h-screen relative overflow-hidden">
      {/* Full-screen sliding image background */}
      <div className="absolute inset-0">
        <div className="relative w-full h-full">
          {heroImages.map((image, index) => (
            <div
              key={index}
              className={`absolute inset-0 transition-opacity duration-1000 ${
                index === currentImage ? 'opacity-100' : 'opacity-0'
              }`}
            >
              <img
                src={image}
                alt={`Hero ${index + 1}`}
                className="w-full h-full object-cover scale-105 animate-slow-zoom"
                onLoad={() => setImageLoaded(true)}
              />
            </div>
          ))}
        </div>
        
        {/* Enhanced gradient overlay for mobile readability */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/20 to-black/70 hidden sm:block"></div>
      
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-black/40 hidden sm:block"></div>
      </div>

      {/* Centered Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 pt-16 sm:pt-20">
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center max-w-4xl mx-auto">
            {/* Mobile text background for better readability */}
            <div className="sm:hidden absolute inset-0 bg-black/50 -z-10 "></div>
            
            {/* Premium Badge */}
            <div className="inline-flex items-center space-x-2 bg-black/20 backdrop-blur-sm px-4 sm:px-6 py-2 sm:py-3 rounded-full border border-burgundy-400/50 mb-6 sm:mb-8 shadow-lg mx-4">
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-3 h-3 sm:w-4 sm:h-4 fill-gold-400 text-gold-400" />
                ))}
              </div>
              <span className="text-white font-medium text-sm sm:text-base">Premium Beauty Experience</span>
            </div>
            
            {/* Main Heading */}
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold leading-tight mb-6 sm:mb-8 max-w-5xl mx-auto px-4">
              <span className="text-5xl lg:text-7xl font-bold font-playfair text-white leading-tight">
                {currentHeroData.title.split(' ').slice(0, 2).join(' ')}
              </span>
              <span className="block text-burgundy-500 drop-shadow-[0_4px_8px_rgba(0,0,0,0.8)] font-playfair text-center">
                {currentHeroData.title.split(' ').slice(2).join(' ')}
              </span>
            </h1>

            {/* Description */}
            <p className="text-base sm:text-lg lg:text-xl xl:text-2xl text-white leading-relaxed max-w-2xl sm:max-w-3xl mx-auto mb-8 sm:mb-12 px-4 drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)] font-inter text-center">
              {currentHeroData.description}
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center mb-12 sm:mb-16 px-4">
              <Button
                size="lg"
                className="bg-gradient-to-r from-burgundy-400 to-burgundy-500 text-black hover:from-burgundy-500 hover:to-burgundy-200 transition-all duration-300 hover:shadow-2xl hover:scale-105 px-6 sm:px-10 py-4 sm:py-6 text-base sm:text-lg font-semibold shadow-xl font-inter w-full sm:w-auto"
                onClick={() => navigate('/products')}
              >
                <Sparkles className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                Explore Products
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-white/80 text-white hover:bg-white hover:text-black transition-all duration-300 hover:scale-105 px-6 sm:px-10 py-4 sm:py-6 text-base sm:text-lg font-semibold backdrop-blur-sm bg-black/50 shadow-xl font-inter w-full sm:w-auto"
                onClick={() => navigate('/contact')}
              >
                <Phone className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                Contact Us
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-8 max-w-4xl mx-auto px-4">
              <div className="text-center bg-black/20 backdrop-blur-sm rounded-2xl p-4 sm:p-6 border border-white/20 shadow-lg">
                <div className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-gold-400 mb-1 sm:mb-2 drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)] font-playfair">
                  {currentHeroData.client.toLocaleString()}+
                </div>
                <div className="text-white text-xs sm:text-sm uppercase tracking-wide font-medium font-inter">Happy Clients</div>
              </div>
              <div className="text-center bg-black/20 backdrop-blur-sm rounded-2xl p-4 sm:p-6 border border-white/20 shadow-lg">
                <div className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-gold-400 mb-1 sm:mb-2 drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)] font-playfair">
                  {currentHeroData.service}+
                </div>
                <div className="text-white text-xs sm:text-sm uppercase tracking-wide font-medium font-inter">Services</div>
              </div>
              <div className="text-center bg-black/20 backdrop-blur-sm rounded-2xl p-4 sm:p-6 border border-white/20 shadow-lg">
                <div className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-gold-400 mb-1 sm:mb-2 drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)] font-playfair">
                  {currentHeroData.experience}+
                </div>
                <div className="text-white text-xs sm:text-sm uppercase tracking-wide font-medium font-inter">Years Experience</div>
              </div>
            </div>

            {/* Image Progress Indicators */}
            <div className="flex justify-center space-x-3 mt-12">
              {heroImages.map((_, index) => (
                <button
                  key={index}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentImage
                      ? 'bg-burgundy-400 scale-125 shadow-lg'
                      : 'bg-white/40 hover:bg-white/20 backdrop-blur-sm'
                  }`}
                  onClick={() => setCurrentImage(index)}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
          <div className="animate-bounce">
            <ArrowDown className="w-6 h-6 text-white/80" />
          </div>
        </div>
      </div>

      <style>{`
        @keyframes slow-zoom {
          0% { transform: scale(1); }
          100% { transform: scale(1.05); }
        }
        
        .animate-slow-zoom {
          animation: slow-zoom 20s ease-in-out infinite alternate;
        }
      `}</style>
    </section>
  );
};

export default Hero;