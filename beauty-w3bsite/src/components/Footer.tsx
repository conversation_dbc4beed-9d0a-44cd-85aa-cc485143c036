import React from 'react';
import { Link } from 'react-router-dom';
import { MapPin, Phone, Mail, Clock, Facebook, Youtube, Instagram, Music2 } from 'lucide-react';
import { useOrganizationInfo } from '@/hooks/useOrganizationInfo';

const Footer = () => {
  const { orgInfo } = useOrganizationInfo();

  const quickLinks = [
    { name: 'Home', path: '/' },
    { name: 'Products', path: '/products' },
    { name: 'Services', path: '/services' },
    { name: 'Gallery', path: '/gallery' },
    { name: 'About', path: '/about' },
    { name: 'Contact', path: '/contact' },
  ];

  const services = [
    'Facial Treatments',
    'Hair Styling',
    'Makeup Services',
    'Bridal Packages',
    'Skin Care',
    'Beauty Consultation'
  ];

  // Don't show loading state in footer - just render with fallback values

  return (
    <footer className="bg-burgundy-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              {orgInfo?.logo && (
                <img 
                  src={orgInfo.logo} 
                  alt={orgInfo.name}
                  className="w-10 h-10 rounded-full object-cover"
                />
              )}
              <h3 className="text-2xl font-bold font-playfair">
                {orgInfo?.name || 'Beauty Parlour'}
              </h3>
            </div>
            <p className="text-burgundy-200 leading-relaxed">
              Experience luxury beauty treatments with our premium services and professional care. 
              Your beauty is our passion.
            </p>
            
            {/* Social Media Links */}
            <div className="flex space-x-4 pt-4">
              {orgInfo?.facebook && (
                <a 
                  href={orgInfo.facebook} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-burgundy-800 rounded-full flex items-center justify-center hover:bg-gold-500 transition-colors"
                >
                  <Facebook className="w-5 h-5" />
                </a>
              )}
              {orgInfo?.instagram && (
                <a 
                  href={orgInfo.instagram} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-burgundy-800 rounded-full flex items-center justify-center hover:bg-gold-500 transition-colors"
                >
                  <Instagram className="w-5 h-5" />
                </a>
              )}
              {orgInfo?.youtube && (
                <a 
                  href={orgInfo.youtube} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-burgundy-800 rounded-full flex items-center justify-center hover:bg-gold-500 transition-colors"
                >
                  <Youtube className="w-5 h-5" />
                </a>
              )}
              {orgInfo?.tiktok && (
                <a 
                  href={orgInfo.tiktok} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-burgundy-800 rounded-full flex items-center justify-center hover:bg-gold-500 transition-colors"
                >
                  <Music2 className="w-5 h-5" />
                </a>
              )}
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold font-playfair text-gold-400">Quick Links</h4>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link 
                    to={link.path}
                    className="text-burgundy-200 hover:text-gold-400 transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold font-playfair text-gold-400">Our Services</h4>
            <ul className="space-y-2">
              {services.map((service) => (
                <li key={service}>
                  <span className="text-burgundy-200">{service}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold font-playfair text-gold-400">Contact Info</h4>
            <div className="space-y-3">
              {orgInfo?.location && (
                <div className="flex items-start space-x-3">
                  <MapPin className="w-5 h-5 text-gold-400 mt-0.5 flex-shrink-0" />
                  <span className="text-burgundy-200">{orgInfo.location}</span>
                </div>
              )}
              {orgInfo?.phone && (
                <div className="flex items-center space-x-3">
                  <Phone className="w-5 h-5 text-gold-400 flex-shrink-0" />
                  <a 
                    href={`tel:${orgInfo.phone}`}
                    className="text-burgundy-200 hover:text-gold-400 transition-colors"
                  >
                    {orgInfo.phone}
                  </a>
                </div>
              )}
              {orgInfo?.email && (
                <div className="flex items-center space-x-3">
                  <Mail className="w-5 h-5 text-gold-400 flex-shrink-0" />
                  <a 
                    href={`mailto:${orgInfo.email}`}
                    className="text-burgundy-200 hover:text-gold-400 transition-colors"
                  >
                    {orgInfo.email}
                  </a>
                </div>
              )}
              <div className="flex items-start space-x-3">
                <Clock className="w-5 h-5 text-gold-400 mt-0.5 flex-shrink-0" />
                <div className="text-burgundy-200">
                  <div>Mon - Sat: 9:00 AM - 8:00 PM</div>
                  <div>Sunday: 10:00 AM - 6:00 PM</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-burgundy-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-burgundy-300 text-sm">
              © 2025 {orgInfo?.name || 'Mah Beauty'}. All rights reserved.
            </div>
            <div className="flex space-x-3 text-sm">
              <p  className="text-green-400  transition-colors">
                Designed and developed by 
              </p>
              <a href="https://nurpratapkarki.com.np/" className="text-gold-500  hover:text-blue-400 transition-colors" target='_blank'>
                Nur Pratap Karki
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
