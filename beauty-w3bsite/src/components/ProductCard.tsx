import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import JsBarcode from 'jsbarcode';
import { QrCode, Download, Star, ShoppingCart, Plus, Minus, Eye, Heart } from 'lucide-react';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';

import { Product } from '@/types/ecommerce';

interface ProductCardProps {
  product: Product;
  onViewDetails?: (product: Product) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const [showBarcode, setShowBarcode] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [isFavorite, setIsFavorite] = useState(false);
  const [isFlipped, setIsFlipped] = useState(false);
  const barcodeRef = useRef<SVGSVGElement>(null);
  const navigate = useNavigate();
  const { addToCart, loading: cartLoading } = useCart();
  const { isAuthenticated } = useAuth();

  const generateEAN13Code = () => {
    if (product.ean_13_code) {
      return product.ean_13_code;
    }

    const country = "99";
    const company = "12345";
    const productCode = product.id.toString().padStart(5, '0');
    const code = country + company + productCode;
    let sum = 0;
    for (let i = 0; i < 12; i++) {
      sum += parseInt(code[i]) * (i % 2 === 0 ? 1 : 3);
    }
    const checkDigit = (10 - (sum % 10)) % 10;
    return code + checkDigit;
  };

  useEffect(() => {
    if (showBarcode && barcodeRef.current && isFlipped) {
      const eanCode = generateEAN13Code();
      JsBarcode(barcodeRef.current, eanCode, {
        format: "EAN13",
        width: 1.8,
        height: 50,
        displayValue: true,
        fontSize: 11,
        textMargin: 4,
        background: "#ffffff",
        lineColor: "#8B1538"
      });
    }
  }, [showBarcode, isFlipped, product.id, product.ean_13_code]);

  const handleAddToCart = async () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
    await addToCart(product.id, quantity);
  };

  const handleBarcodeToggle = () => {
    setShowBarcode(!showBarcode);
    setIsFlipped(!isFlipped);
  };

  const isInStock = product.in_stock ?? true;
  const getCategoryName = () => {
    if (typeof product.category === 'object' && product.category?.name) {
      return product.category.name;
    }
    return product.category_name || '';
  };

  const currentPrice = product.is_on_offer && product.offer_price ? product.offer_price : product.price;

  const getProductImage = () => {
    if (product.image_url || product.image) {
      return product.image_url || product.image;
    }
    const categoryImages: { [key: string]: string } = {
      'Skincare': 'https://images.unsplash.com/photo-1556228720-195a672e8a03?w=400&h=400&fit=crop',
      'Makeup': 'https://images.unsplash.com/photo-1586495777744-4413f21062fa?w=400&h=400&fit=crop',
      'Hair Care': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop',
    };
    return categoryImages[getCategoryName()] || 'https://via.placeholder.com/400x300/8B1538/FFFFFF?text=Beauty+Product';
  };

  const downloadBarcode = () => {
    if (barcodeRef.current) {
      const svgData = new XMLSerializer().serializeToString(barcodeRef.current);
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);
        const pngFile = canvas.toDataURL('image/png');
        const downloadLink = document.createElement('a');
        downloadLink.download = `${product.name}-EAN13-${generateEAN13Code()}.png`;
        downloadLink.href = pngFile;
        downloadLink.click();
      };
      img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
    }
  };

  return (
    <div className="group relative">
      <div
        className={`relative w-full transition-transform duration-500 transform-gpu ${
          isFlipped ? '[transform:rotateY(180deg)]' : ''
        }`}
        style={{ transformStyle: 'preserve-3d' }}
      >
        {/* Front Side */}
        <Card className={`relative bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 rounded-2xl overflow-hidden ${isFlipped ? '[backface-visibility:hidden]' : ''}`}>
          {/* Image Section */}
          <div className="relative h-56 overflow-hidden bg-gray-100">
            <img
              src={getProductImage()}
              alt={product.name}
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = 'https://via.placeholder.com/400x300/8B1538/FFFFFF?text=Beauty+Product';
              }}
            />
            
            {/* Top badges */}
            <div className="absolute top-3 left-3 flex flex-col gap-1">
              {!isInStock && (
                <Badge className="bg-red-500 text-white text-xs font-medium">
                  Sold Out
                </Badge>
              )}
              {product.is_on_offer && (
                <Badge className="bg-emerald-500 text-white text-xs font-medium">
                  Sale
                </Badge>
              )}
            </div>

            {/* Action Icons */}
            <div className="absolute top-3 right-3 flex gap-2">
              <button
                onClick={() => setIsFavorite(!isFavorite)}
                className="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-sm hover:shadow-md transition-all"
              >
                <Heart className={`w-4 h-4 ${isFavorite ? 'fill-rose-500 text-rose-500' : 'text-gray-600'}`} />
              </button>
              <button
                onClick={handleBarcodeToggle}
                className="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-sm hover:shadow-md transition-all"
              >
                <QrCode className="w-4 h-4 text-gray-600" />
              </button>
            </div>

            {/* Bottom info overlay */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-burgundy-600/60 to-transparent p-4">
              <div className="flex justify-between items-end text-white">
                <div className="text-xs opacity-90">{getCategoryName()}</div>
                <div className="flex items-center gap-1 bg-white/20 backdrop-blur-sm px-2 py-1 rounded-full">
                  <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                  <span className="text-xs font-medium">{product.rating}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Content */}
          <CardContent className="p-4 space-y-3">
            <h3 className="font-semibold text-gray-900 line-clamp-2 leading-tight">
              {product.name}
            </h3>
            
            <p className="text-sm text-gray-600 line-clamp-2">
              {product.description}
            </p>

            <div className="flex items-center justify-between">
              <div className="flex items-baseline gap-2">
                <span className="text-xl font-bold text-gray-900">Rs. {currentPrice}</span>
                {product.is_on_offer && product.offer_price && (
                  <span className="text-sm text-gray-400 line-through">Rs. {product.price}</span>
                )}
              </div>
              {product.is_on_offer && product.offer_price && (
                <Badge variant="secondary" className="text-xs bg-green-50 text-green-700">
                  {Math.round(((parseFloat(product.price) - parseFloat(product.offer_price)) / parseFloat(product.price)) * 100)}% OFF
                </Badge>
              )}
            </div>
          </CardContent>

          {/* Footer */}
          <CardFooter className="p-4 pt-0 space-y-3">
            {/* Quantity */}
            {isInStock && (
              <div className="flex items-center justify-center gap-3 bg-gray-50 rounded-lg p-3">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  disabled={quantity <= 1}
                  className="w-7 h-7 rounded-full bg-white border border-gray-200 flex items-center justify-center disabled:opacity-50 hover:bg-gray-50 transition-colors"
                >
                  <Minus className="w-3 h-3" />
                </button>
                <span className="font-semibold text-gray-900 min-w-[2rem] text-center">{quantity}</span>
                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="w-7 h-7 rounded-full bg-white border border-gray-200 flex items-center justify-center hover:bg-gray-50 transition-colors"
                >
                  <Plus className="w-3 h-3" />
                </button>
              </div>
            )}

            {/* Buttons */}
            <div className="grid grid-cols-4 gap-2">
              <Button
                className="col-span-3 burgundy-600 hover:burgundy-400 text-white h-10 text-sm font-medium"
                onClick={handleAddToCart}
                disabled={!isInStock || cartLoading}
              >
                <ShoppingCart className="w-4 h-4 mr-2" />
                {!isInStock ? 'Sold Out' : cartLoading ? 'Adding...' : 'Add to Cart'}
              </Button>
              <Button
                variant="outline"
                className="col-span-1 h-10 border-gray-200 hover:bg-gray-50"
                onClick={() => navigate(`/products/${product.id}`)}
                title="View Details"
              >
                <Eye className="w-4 h-4" />
              </Button>
            </div>
          </CardFooter>
        </Card>

        {/* Back Side - Barcode */}
        <Card className={`absolute inset-0 bg-white border-0 shadow-lg rounded-2xl ${!isFlipped ? '[backface-visibility:hidden]' : ''} [transform:rotateY(180deg)]`}>
          <div className="h-full flex flex-col items-center justify-center p-6 space-y-6">
            <div className="text-center">
              <h4 className="font-semibold text-gray-900 mb-1">Product Barcode</h4>
              <p className="text-sm text-gray-600">EAN-13 Code</p>
            </div>
            
            <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
              <svg ref={barcodeRef}></svg>
            </div>
            
            <div className="text-center">
              <p className="text-xs text-gray-500 mb-1">Code:</p>
              <p className="font-mono text-sm font-semibold text-gray-900">{generateEAN13Code()}</p>
            </div>
            
            <div className="flex gap-2 w-full">
              <Button
                onClick={downloadBarcode}
                className="flex-1 burgundy-600 hover:burgundy-400 text-white h-10 text-sm"
              >
                <Download className="w-4 h-4 mr-1" />
                Download
              </Button>
              <Button
                onClick={handleBarcodeToggle}
                variant="outline"
                className="flex-1 border-gray-200 hover:bg-gray-50 h-10 text-sm"
              >
                Back
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ProductCard;