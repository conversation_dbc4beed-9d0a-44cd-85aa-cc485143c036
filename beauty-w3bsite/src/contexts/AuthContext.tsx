import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  loginUser,
  registerUser,
  logoutUser,
  getCurrentUser,
  updateUserProfile,
  changePassword,
  resetPassword,
  googleLogin,
  setAuthToken,
  getAuthToken,
  isAuthenticated as checkIsAuthenticated
} from '@/api/apiService';
import { User, AuthContextType, LoginCredentials, RegisterData } from '@/types/ecommerce';
import { toast } from 'react-toastify';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Initialize auth state on mount
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = getAuthToken();
        if (token && checkIsAuthenticated()) {
          await refreshUser();
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        // Clear invalid token
        setAuthToken(null);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await loginUser(credentials);

      if (response.status === "200" && response.data?.access) {
        setAuthToken(response.data.access);
        if (response.data.refresh) {
          localStorage.setItem('refreshToken', response.data.refresh);
        }

        // Fetch user profile
        await refreshUser();
        setIsAuthenticated(true);
        toast.success('Login successful!');
        return true;
      } else {
        toast.error(response.message || 'Login failed');
        return false;
      }
    } catch (error: any) {
      toast.error(error.message || 'Login failed');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const loginWithGoogle = async (credential: string): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await googleLogin(credential);

      if (response.status === "200" && response.data?.access) {
        setAuthToken(response.data.access);
        if (response.data.refresh) {
          localStorage.setItem('refreshToken', response.data.refresh);
        }

        // Fetch user profile
        await refreshUser();
        setIsAuthenticated(true);
        toast.success('Google login successful!');
        return true;
      } else {
        toast.error(response.message || 'Google login failed');
        return false;
      }
    } catch (error: any) {
      toast.error(error.message || 'Google login failed');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData: RegisterData): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await registerUser(userData);
      
      if (response.status === "200") {
        toast.success('Registration successful! Please login.');
        return true;
      } else {
        toast.error(response.message || 'Registration failed');
        return false;
      }
    } catch (error: any) {
      toast.error(error.message || 'Registration failed');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await logoutUser();
      setUser(null);
      setIsAuthenticated(false);
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear local state even if API call fails
      setUser(null);
      setIsAuthenticated(false);
    }
  };

  const updateProfile = async (userData: Partial<User>): Promise<boolean> => {
    try {
      const response = await updateUserProfile(userData);

      if (response.status === "200") {
        await refreshUser(); // Refresh user data
        toast.success('Profile updated successfully!');
        return true;
      } else {
        toast.error(response.message || 'Profile update failed');
        return false;
      }
    } catch (error: any) {
      toast.error(error.message || 'Profile update failed');
      return false;
    }
  };

  const changeUserPassword = async (passwordData: {
    current_password: string;
    new_password: string;
    re_new_password: string;
  }): Promise<boolean> => {
    try {
      const response = await changePassword(passwordData);

      if (response.status === "200") {
        toast.success('Password changed successfully!');
        return true;
      } else {
        toast.error(response.message || 'Password change failed');
        return false;
      }
    } catch (error: any) {
      toast.error(error.message || 'Password change failed');
      return false;
    }
  };

  const requestPasswordReset = async (email: string): Promise<boolean> => {
    try {
      const response = await resetPassword(email);

      if (response.status === "200") {
        toast.success('Password reset email sent!');
        return true;
      } else {
        toast.error(response.message || 'Password reset failed');
        return false;
      }
    } catch (error: any) {
      toast.error(error.message || 'Password reset failed');
      return false;
    }
  };

  const refreshUser = async () => {
    try {
      const response = await getCurrentUser();
      
      if (response.success && response.data) {
        setUser(response.data);
        setIsAuthenticated(true);
      } else {
        // Invalid token or user not found
        setUser(null);
        setIsAuthenticated(false);
        setAuthToken(null);
      }
    } catch (error) {
      console.error('Refresh user error:', error);
      setUser(null);
      setIsAuthenticated(false);
      setAuthToken(null);
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    loading,
    login,
    loginWithGoogle,
    register,
    logout,
    updateProfile,
    changePassword: changeUserPassword,
    requestPasswordReset,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
