import React, { useMemo, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Hero from '@/components/Hero';
import ProductCard from '@/components/ProductCard';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star, Award, Users, Clock, ArrowRight, ChevronLeft, ChevronRight, Sparkles, Scissors, Heart } from 'lucide-react';
import { getProductsFromBackend, getTestimonials } from '@/api/apiService';
import { Product } from '@/types/ecommerce';
import { ServiceCategory, Testimonial } from '@/data/mockData';

const Home = () => {
  const navigate = useNavigate();
  
  // States for featured content
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [featuredServices, setFeaturedServices] = useState<ServiceCategory[]>([]);
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [loading, setLoading] = useState(true);

  // Memoized data fetching
  const fetchData = useMemo(() => async () => {
    try {
      setLoading(true);
      
      // Fetch all products and take first 6
      const productsResponse = await getProductsFromBackend();
      if (productsResponse && productsResponse.success) {
        const products = productsResponse.data || [];
        setFeaturedProducts(products.slice(0, 6));
      }

      // Services functionality removed - not available in backend
      setFeaturedServices([]);

      // Fetch all testimonials and take first 6
      const testimonialsResponse = await getTestimonials();
      if (testimonialsResponse && testimonialsResponse.success) {
        const testimonials = testimonialsResponse.data || [];
        setTestimonials(testimonials.slice(0, 6));
      }
    } catch (error) {
      // Set empty arrays on error to prevent undefined errors
      setFeaturedProducts([]);
      setFeaturedServices([]);
      setTestimonials([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Auto-play testimonial carousel
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, testimonials.length]);

  // Memoized handlers
  const handleProductDetails = useMemo(() => (product: any) => {
    navigate('/products');
  }, [navigate]);

  const handleServiceClick = useMemo(() => (serviceId: number) => {
    navigate('/services');
  }, [navigate]);

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  return (
    <div className="pt-16">
      <Hero />
      
      {/* Featured Products Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold font-playfair text-burgundy-800 mb-4">
              Featured Products
            </h2>
            <p className="text-xl text-burgundy-600 max-w-2xl mx-auto">
              Discover our most popular luxury beauty products
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {loading ? (
              // Loading skeleton
              Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="bg-white/50 rounded-lg h-96 animate-pulse"></div>
              ))
            ) : (
              featuredProducts.slice(0, 6).map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  onViewDetails={handleProductDetails}
                />
              ))
            )}
          </div>

          <div className="text-center">
            <Button 
              size="lg"
              onClick={() => navigate('/products')}
              className="luxury-gradient text-white hover:opacity-90 px-8 py-4 text-lg"
            >
              View All Products
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
          </div>
        </div>
      </section>

      {/* Explorable Services Section */}
      <section className="py-20 cream-gradient">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold font-playfair text-burgundy-800 mb-4">
              Our Premium Services
            </h2>
            <p className="text-xl text-burgundy-600 max-w-2xl mx-auto">
              Experience luxury beauty treatments crafted with precision and care
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            {loading ? (
              // Loading skeleton
              Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="bg-white/50 rounded-lg h-80 animate-pulse"></div>
              ))
            ) : (
              featuredServices.flatMap(category => 
                category.services?.filter(service => service?.popular).slice(0, 4) || []
              ).filter(service => service).map((service) => (
              <Card 
                key={service.id} 
                className="group hover-lift bg-white/90 backdrop-blur-sm border-cream-300 overflow-hidden cursor-pointer"
                onClick={() => handleServiceClick(service.id)}
              >
                <div className="relative">
                  <img
                    src={service.image}
                    alt={service.name}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-burgundy-900/50 to-transparent"></div>
                  
                  {service.popular && (
                    <Badge className="absolute top-3 left-3 bg-gold-500 text-white">
                      Most Popular
                    </Badge>
                  )}

                  <div className="absolute bottom-4 left-4 text-white">
                    <div className="text-xl font-bold font-playfair">From {service.price}</div>
                  </div>

                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                      {service.category === 'Facial Treatments' && <Sparkles className="w-8 h-8 text-white" />}
                      {service.category === 'Hair Services' && <Scissors className="w-8 h-8 text-white" />}
                      {service.category === 'Makeup Services' && <Heart className="w-8 h-8 text-white" />}
                      {!['Facial Treatments', 'Hair Services', 'Makeup Services'].includes(String(service.category)) && <Star className="w-8 h-8 text-white" />}
                    </div>
                  </div>
                </div>

                <CardContent className="p-6 space-y-3">
                  <h3 className="text-lg font-semibold font-playfair text-burgundy-800">
                    {service.name}
                  </h3>
                  <p className="text-burgundy-600 text-sm line-clamp-4">
                    {service.description}
                  </p>
                  <div className="flex items-center text-burgundy-500 text-sm">
                    <Clock className="w-4 h-4 mr-2" />
                    {service.duration}
                  </div>
                </CardContent>
              </Card>
              ))
            )}
          </div>

          <div className="text-center">
            <Button 
              size="lg"
              onClick={() => navigate('/services')}
              className="luxury-gradient text-white hover:opacity-90 px-8 py-4 text-lg"
            >
              Explore All Services
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 cream-gradient">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold font-playfair text-burgundy-800 mb-4">
              Why Choose Mah Beauty
            </h2>
          </div>

          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold font-playfair text-burgundy-800 mb-2">
                Expert Professionals
              </h3>
              <p className="text-burgundy-600">
                Certified and experienced beauty experts
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold font-playfair text-burgundy-800 mb-2">
                Premium Products
              </h3>
              <p className="text-burgundy-600">
                Only the finest beauty products and tools
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold font-playfair text-burgundy-800 mb-2">
                Personalized Care
              </h3>
              <p className="text-burgundy-600">
                Tailored treatments for your unique needs
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold font-playfair text-burgundy-800 mb-2">
                Flexible Timing
              </h3>
              <p className="text-burgundy-600">
                Convenient appointment scheduling
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Large Testimonial Carousel */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold font-playfair text-burgundy-800 mb-4">
              What Our Clients Say
            </h2>
            <p className="text-xl text-burgundy-600">
              Real experiences from our valued customers
            </p>
          </div>

          <div 
            className="relative max-w-4xl mx-auto"
            onMouseEnter={() => setIsAutoPlaying(false)}
            onMouseLeave={() => setIsAutoPlaying(true)}
          >
            {testimonials.length > 0 && testimonials[currentTestimonial] && (
              <Card className="bg-cream-100 border-cream-300 overflow-hidden">
                <CardContent className="p-12 text-center">
                  <div className="mb-8">
                    <img
                      src={testimonials[currentTestimonial].image}
                      alt={testimonials[currentTestimonial].name}
                      className="w-24 h-24 rounded-full mx-auto mb-6 object-cover border-4 border-gold-400"
                    />
                    <div className="flex items-center justify-center mb-6">
                      {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                        <Star key={i} className="w-6 h-6 fill-gold-400 text-gold-400 mx-1" />
                      ))}
                    </div>
                  </div>

                  <blockquote className="text-2xl font-playfair text-burgundy-800 mb-8 leading-relaxed">
                    "{testimonials[currentTestimonial].comment}"
                  </blockquote>

                  <div className="border-t border-cream-300 pt-6">
                    <div className="font-semibold text-xl text-burgundy-800 mb-2">
                      {testimonials[currentTestimonial].name}
                    </div>
                    <div className="text-burgundy-600">
                      {testimonials[currentTestimonial].service}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Navigation Arrows */}
            {testimonials.length > 0 && (
              <>
                <Button
                  variant="outline"
                  size="icon"
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm border-burgundy-200 hover:bg-burgundy-50"
                  onClick={prevTestimonial}
                >
                  <ChevronLeft className="w-5 h-5 text-burgundy-800" />
                </Button>

                <Button
                  variant="outline"
                  size="icon"
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm border-burgundy-200 hover:bg-burgundy-50"
                  onClick={nextTestimonial}
                >
                  <ChevronRight className="w-5 h-5 text-burgundy-800" />
                </Button>

                {/* Navigation Dots */}
                <div className="flex justify-center mt-8 space-x-2">
                  {testimonials.map((_, index) => (
                    <button
                      key={index}
                      className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                        index === currentTestimonial
                          ? 'bg-burgundy-800'
                          : 'bg-burgundy-200 hover:bg-burgundy-400'
                      }`}
                      onClick={() => setCurrentTestimonial(index)}
                      aria-label={`Go to testimonial ${index + 1}`}
                    />
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 luxury-gradient text-white">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl font-bold font-playfair mb-4">
            Ready to Transform Your Look?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Book your appointment today and experience the luxury of premium beauty care
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-white text-burgundy-800 hover:bg-cream-100 px-8 py-4 text-lg"
              onClick={() => navigate('/contact')}
            >
              Book Appointment
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-2 border-white text-burgundy-800 hover:bg-burgundy-800 hover:text-white px-8 py-4 text-lg"
              onClick={() => window.open('tel:+977-9800000000', '_self')}
            >
              Call Now
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;