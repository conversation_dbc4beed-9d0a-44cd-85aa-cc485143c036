import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  Package, 
  Truck, 
  CheckCircle, 
  Clock, 
  XCircle,
  MapPin,
  Phone,
  Mail,
  Calendar,
  CreditCard,
  FileText
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getOrderById } from '@/api/apiService';
import ErrorBoundary from '@/components/ErrorBoundary';

interface OrderItem {
  id: number;
  product: {
    id: number;
    name: string;
    image?: string;
    image_url?: string;
    category_name?: string;
  };
  variation: Array<{
    id: number;
    variation_category: string;
    variation_value: string;
  }>;
  quantity: number;
  product_price: number;
  ordered: boolean;
}

interface Order {
  id: number;
  order_number: string;
  first_name: string;
  last_name: string;
  full_name: string;
  phone: string;
  email: string;
  state: string;
  area: string;
  address: string;
  status: string;
  status_display: string;
  grand_total: number;
  tax: number;
  order_note?: string;
  created_at: string;
  order_items_count: number;
  order_items: OrderItem[];
  payment?: {
    id: number;
    payment_id: string;
    payment_method_details?: {
      name: string;
      image?: string;
    };
  };
}

const OrderDetails = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    if (!orderId) {
      toast.error('Order ID is required');
      navigate('/orders');
      return;
    }

    fetchOrderDetails();
  }, [orderId, isAuthenticated, navigate]);

  const fetchOrderDetails = async () => {
    try {
      setLoading(true);
      const response = await getOrderById(parseInt(orderId!));

      if (response.success && response.data) {
        setOrder(response.data);
      } else {
        toast.error('Failed to load order details');
        navigate('/orders');
      }
    } catch (error) {
      console.error('Error fetching order details:', error);
      toast.error('Failed to load order details');
      navigate('/orders');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'processing':
        return <Clock className="w-5 h-5 text-blue-600" />;
      case 'packed':
        return <Package className="w-5 h-5 text-orange-600" />;
      case 'shipped':
        return <Truck className="w-5 h-5 text-purple-600" />;
      case 'delivered':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'cancel':
        return <XCircle className="w-5 h-5 text-red-600" />;
      default:
        return <Clock className="w-5 h-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'packed':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'shipped':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancel':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatPrice = (price: number | string) => {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return numPrice.toLocaleString('en-NP');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="pt-20 min-h-screen bg-cream-200 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-burgundy-600 mx-auto mb-4"></div>
          <p className="text-burgundy-600">Loading order details...</p>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="pt-20 min-h-screen bg-cream-200 flex items-center justify-center">
        <Card className="max-w-md w-full mx-4 bg-white/90 backdrop-blur-sm border-cream-300">
          <CardContent className="text-center p-8">
            <XCircle className="w-16 h-16 text-red-600 mx-auto mb-4" />
            <h2 className="text-xl font-playfair font-semibold text-burgundy-800 mb-2">
              Order Not Found
            </h2>
            <p className="text-burgundy-600 mb-4">
              The order you're looking for doesn't exist or you don't have permission to view it.
            </p>
            <Button
              onClick={() => navigate('/orders')}
              className="luxury-gradient text-white hover:opacity-90"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Orders
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const subtotal = order.grand_total - order.tax;

  return (
    <div className="pt-20 min-h-screen bg-cream-200">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => navigate('/orders')}
              className="border-burgundy-200 text-burgundy-700 hover:bg-burgundy-50"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Orders
            </Button>
            <div>
              <h1 className="text-3xl font-playfair font-bold text-burgundy-800">
                Order Details
              </h1>
              <p className="text-burgundy-600">Order #{order.order_number}</p>
            </div>
          </div>
          
          <Badge className={`px-4 py-2 text-sm font-medium border ${getStatusColor(order.status)}`}>
            <span className="flex items-center gap-2">
              {getStatusIcon(order.status)}
              {order.status_display}
            </span>
          </Badge>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Status Timeline */}
            <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
              <CardHeader>
                <CardTitle className="text-xl font-playfair text-burgundy-800 flex items-center gap-2">
                  <Package className="w-5 h-5" />
                  Order Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  {['Processing', 'Packed', 'Shipped', 'Delivered'].map((status, index) => {
                    const isActive = ['processing', 'packed', 'shipped', 'delivered'].indexOf(order.status.toLowerCase()) >= index;
                    const isCurrent = order.status.toLowerCase() === status.toLowerCase();
                    
                    return (
                      <div key={status} className="flex flex-col items-center flex-1">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center border-2 ${
                          isActive 
                            ? 'bg-burgundy-600 border-burgundy-600 text-white' 
                            : 'bg-gray-100 border-gray-300 text-gray-400'
                        } ${isCurrent ? 'ring-4 ring-burgundy-200' : ''}`}>
                          {getStatusIcon(status)}
                        </div>
                        <span className={`text-xs mt-2 font-medium ${
                          isActive ? 'text-burgundy-800' : 'text-gray-400'
                        }`}>
                          {status}
                        </span>
                        {index < 3 && (
                          <div className={`h-0.5 w-full mt-4 ${
                            isActive ? 'bg-burgundy-600' : 'bg-gray-300'
                          }`} />
                        )}
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Order Items */}
            <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
              <CardHeader>
                <CardTitle className="text-xl font-playfair text-burgundy-800">
                  Order Items ({order.order_items_count})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {order.order_items.map((item) => (
                  <div key={item.id} className="flex gap-4 p-4 border border-cream-200 rounded-lg">
                    <div className="flex-shrink-0">
                      <img
                        src={item.product.image_url || item.product.image || 'https://via.placeholder.com/80x80/8B1538/FFFFFF?text=No+Image'}
                        alt={item.product.name}
                        className="w-20 h-20 object-cover rounded-lg"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = 'https://via.placeholder.com/80x80/8B1538/FFFFFF?text=No+Image';
                        }}
                      />
                    </div>
                    <div className="flex-grow">
                      <h3 className="font-semibold text-burgundy-800">{item.product.name}</h3>
                      {item.product.category_name && (
                        <Badge className="mt-1 bg-burgundy-100 text-burgundy-800 text-xs">
                          {item.product.category_name}
                        </Badge>
                      )}
                      {item.variation.length > 0 && (
                        <div className="mt-2 space-y-1">
                          {item.variation.map((variation) => (
                            <span key={variation.id} className="text-sm text-burgundy-600">
                              {variation.variation_category}: {variation.variation_value}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-burgundy-800">
                        Rs. {formatPrice(item.product_price)}
                      </p>
                      <p className="text-sm text-burgundy-600">Qty: {item.quantity}</p>
                      <p className="text-sm font-medium text-burgundy-800">
                        Total: Rs. {formatPrice(item.product_price * item.quantity)}
                      </p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Order Summary */}
            <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
              <CardHeader>
                <CardTitle className="text-xl font-playfair text-burgundy-800">
                  Order Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-burgundy-600">Subtotal:</span>
                  <span className="font-medium text-burgundy-800">Rs. {formatPrice(subtotal)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-burgundy-600">Tax (13%):</span>
                  <span className="font-medium text-burgundy-800">Rs. {formatPrice(order.tax)}</span>
                </div>
                <Separator />
                <div className="flex justify-between text-lg font-semibold">
                  <span className="text-burgundy-800">Total:</span>
                  <span className="text-burgundy-800">Rs. {formatPrice(order.grand_total)}</span>
                </div>
              </CardContent>
            </Card>

            {/* Order Information */}
            <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
              <CardHeader>
                <CardTitle className="text-xl font-playfair text-burgundy-800">
                  Order Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Calendar className="w-4 h-4 text-burgundy-600" />
                  <div>
                    <p className="text-sm text-burgundy-600">Order Date</p>
                    <p className="font-medium text-burgundy-800">{formatDate(order.created_at)}</p>
                  </div>
                </div>
                
                {order.payment && (
                  <div className="flex items-center gap-3">
                    <CreditCard className="w-4 h-4 text-burgundy-600" />
                    <div>
                      <p className="text-sm text-burgundy-600">Payment Method</p>
                      <p className="font-medium text-burgundy-800">
                        {order.payment.payment_method_details?.name || 'N/A'}
                      </p>
                      {order.payment.payment_id && (
                        <p className="text-xs text-burgundy-500">ID: {order.payment.payment_id}</p>
                      )}
                    </div>
                  </div>
                )}

                {order.order_note && (
                  <div className="flex items-start gap-3">
                    <FileText className="w-4 h-4 text-burgundy-600 mt-1" />
                    <div>
                      <p className="text-sm text-burgundy-600">Order Note</p>
                      <p className="font-medium text-burgundy-800">{order.order_note}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Delivery Address */}
            <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
              <CardHeader>
                <CardTitle className="text-xl font-playfair text-burgundy-800">
                  Delivery Address
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start gap-3">
                  <MapPin className="w-4 h-4 text-burgundy-600 mt-1" />
                  <div>
                    <p className="font-medium text-burgundy-800">{order.full_name}</p>
                    <p className="text-burgundy-600">{order.address}</p>
                    <p className="text-burgundy-600">{order.area}, {order.state}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Phone className="w-4 h-4 text-burgundy-600" />
                  <p className="text-burgundy-800">{order.phone}</p>
                </div>
                
                <div className="flex items-center gap-3">
                  <Mail className="w-4 h-4 text-burgundy-600" />
                  <p className="text-burgundy-800">{order.email}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default () => (
  <ErrorBoundary>
    <OrderDetails />
  </ErrorBoundary>
);
