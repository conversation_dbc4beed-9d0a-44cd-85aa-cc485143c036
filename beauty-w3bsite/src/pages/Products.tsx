
import { useState, useMemo, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter } from 'lucide-react';
import ProductCard from '@/components/ProductCard';
import { getProductsFromBackend, getProductCategories } from '@/api/apiService';
import { Product } from '@/types/ecommerce';

const Products = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<string[]>(['all']);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [loading, setLoading] = useState(true);

  // Fetch products and categories on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch products
        const productsResponse = await getProductsFromBackend();
        if (productsResponse && productsResponse.success) {
          const products = productsResponse.data || [];
          setProducts(products);
          setFilteredProducts(products);
        }

        // Fetch categories
        const categoriesResponse = await getProductCategories();
        if (categoriesResponse && categoriesResponse.success) {
          const categoryData = categoriesResponse.data || [];
          const categoryNames = categoryData.map((cat: any) => cat.name);
          setCategories(['all', ...categoryNames]);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        setProducts([]);
        setFilteredProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Memoized filtering with API data
  const memoizedFilteredProducts = useMemo(() => {
    return products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (product.description && product.description.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesCategory = selectedCategory === 'all' || product.category_name === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  }, [products, searchTerm, selectedCategory]);

  // Update filtered products when memoized result changes
  useEffect(() => {
    setFilteredProducts(memoizedFilteredProducts);
  }, [memoizedFilteredProducts]);

  return (
    <div className="pt-20 min-h-screen bg-cream-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold font-playfair text-burgundy-800 mb-4">
            Premium Beauty Products
          </h1>
          <p className="text-xl text-burgundy-600 max-w-2xl mx-auto">
            Discover our curated collection of luxury beauty products, each with QR codes for easy access
          </p>
        </div>

        {/* Search and Filter */}
        <div className="mb-8 flex flex-col md:flex-row gap-4 items-center justify-between">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-burgundy-400 w-5 h-5" />
            <Input
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 border-burgundy-200 focus:border-burgundy-400"
            />
          </div>
          
          <div className="flex items-center gap-4">
            <Filter className="w-5 h-5 text-burgundy-600" />
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48 border-burgundy-200">
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent className="bg-white border-burgundy-200">
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {loading ? (
            // Loading skeleton
            Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-white/50 rounded-lg h-96 animate-pulse"></div>
            ))
          ) : (
            filteredProducts.map((product) => (
              <ProductCard
                key={product.id}
                product={product}
              />
            ))
          )}
        </div>

        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-burgundy-600 text-lg">
              No products found matching your criteria or Coming soon
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Products;
