import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CheckCircle, Package, Truck, MapPin, CreditCard, ArrowLeft, Download } from 'lucide-react';
import QRCode from 'react-qr-code';

const OrderConfirmation = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [orderData, setOrderData] = useState<any>(null);
  const [showQR, setShowQR] = useState(false);

  useEffect(() => {
    const data = location.state?.orderData;
    if (!data) {
      navigate('/');
      return;
    }
    setOrderData(data);
  }, [location.state, navigate]);

  const generateQRValue = () => {
    if (!orderData) return '';
    
    return JSON.stringify({
      order_number: orderData.order_number,
      total: orderData.grand_total,
      customer: `${orderData.first_name} ${orderData.last_name}`,
      contact: "Contact for payment confirmation"
    });
  };

  const downloadQR = () => {
    const svg = document.getElementById('order-qr-code');
    if (svg) {
      const svgData = new XMLSerializer().serializeToString(svg);
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);
        
        const pngFile = canvas.toDataURL('image/png');
        const downloadLink = document.createElement('a');
        downloadLink.download = `order-${orderData?.order_number}-QR.png`;
        downloadLink.href = pngFile;
        downloadLink.click();
      };
      
      img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
    }
  };

  if (!orderData) {
    return null;
  }

  return (
    <div className="pt-20 min-h-screen bg-cream-200">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <CheckCircle className="w-16 h-16 text-green-600" />
          </div>
          <h1 className="text-3xl font-playfair font-bold text-burgundy-800 mb-2">
            Order Confirmed!
          </h1>
          <p className="text-burgundy-600">
            Thank you for your order. We'll send you a confirmation email shortly.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Order Details */}
          <div className="space-y-6">
            <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
              <CardHeader>
                <CardTitle className="flex items-center text-burgundy-800">
                  <Package className="w-5 h-5 mr-2" />
                  Order Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-burgundy-600">Order Number</p>
                    <p className="font-semibold text-burgundy-800">{orderData.order_number}</p>
                  </div>
                  <div>
                    <p className="text-sm text-burgundy-600">Status</p>
                    <Badge className="bg-yellow-100 text-yellow-800">
                      {orderData.status || 'Processing'}
                    </Badge>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm text-burgundy-600">Order Total</p>
                  <p className="text-2xl font-bold text-burgundy-800">
                    Rs. {orderData.grand_total?.toFixed(2) || '0.00'}
                  </p>
                </div>
                
                {orderData.order_note && (
                  <div>
                    <p className="text-sm text-burgundy-600">Order Notes</p>
                    <p className="text-burgundy-800">{orderData.order_note}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
              <CardHeader>
                <CardTitle className="flex items-center text-burgundy-800">
                  <Truck className="w-5 h-5 mr-2" />
                  Delivery Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm text-burgundy-600">Customer</p>
                  <p className="font-semibold text-burgundy-800">
                    {orderData.first_name} {orderData.last_name}
                  </p>
                </div>
                
                <div>
                  <p className="text-sm text-burgundy-600">Contact</p>
                  <p className="text-burgundy-800">{orderData.email}</p>
                  <p className="text-burgundy-800">{orderData.phone}</p>
                </div>
                
                <div>
                  <p className="text-sm text-burgundy-600">Delivery Address</p>
                  <div className="text-burgundy-800">
                    <p>{orderData.address}</p>
                    <p>{orderData.area}, {orderData.state}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
              <CardHeader>
                <CardTitle className="flex items-center text-burgundy-800">
                  <CreditCard className="w-5 h-5 mr-2" />
                  Payment Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-800 mb-2">Payment Instructions</h4>
                  <p className="text-blue-700 text-sm mb-3">
                    Please complete your payment using one of the following methods:
                  </p>
                  <ul className="text-blue-700 text-sm space-y-1">
                    <li>• Scan the QR code for digital payment</li>
                    <li>• Cash on Delivery (COD) available</li>
                    <li>• Contact us for other payment options</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* QR Code and Actions */}
          <div className="space-y-6">
            <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
              <CardHeader>
                <CardTitle className="text-burgundy-800">Payment QR Code</CardTitle>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <div className="flex justify-center">
                  <QRCode
                    id="order-qr-code"
                    value={generateQRValue()}
                    size={200}
                    fgColor="#8B1538"
                    bgColor="#FFFFFF"
                    title={`QR Code for Order ${orderData.order_number}`}
                  />
                </div>
                
                <p className="text-sm text-burgundy-600">
                  Scan this QR code to view order details or make payment
                </p>
                
                <Button
                  variant="outline"
                  onClick={downloadQR}
                  className="border-burgundy-300 text-burgundy-700 hover:bg-burgundy-50"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download QR Code
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
              <CardHeader>
                <CardTitle className="text-burgundy-800">What's Next?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-burgundy-800 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      1
                    </div>
                    <div>
                      <p className="font-medium text-burgundy-800">Order Confirmation</p>
                      <p className="text-sm text-burgundy-600">You'll receive an email confirmation</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-burgundy-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      2
                    </div>
                    <div>
                      <p className="font-medium text-burgundy-800">Processing</p>
                      <p className="text-sm text-burgundy-600">We'll prepare your order for shipping</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-burgundy-400 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      3
                    </div>
                    <div>
                      <p className="font-medium text-burgundy-800">Delivery</p>
                      <p className="text-sm text-burgundy-600">Your order will be delivered to your address</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="space-y-3">
              <Button
                className="w-full luxury-gradient text-white hover:opacity-90"
                onClick={() => navigate('/orders')}
              >
                View My Orders
              </Button>
              
              <Button
                variant="outline"
                className="w-full border-burgundy-300 text-burgundy-700 hover:bg-burgundy-50"
                onClick={() => navigate('/products')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Continue Shopping
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderConfirmation;
