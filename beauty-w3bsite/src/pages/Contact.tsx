import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MapPin, Phone, Clock, Mail } from 'lucide-react';
import { submitContactForm } from '@/api/apiService';
import { useOrganizationInfo } from '@/hooks/useOrganizationInfo';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    service: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { orgInfo } = useOrganizationInfo();

  // Dynamic contact info based on organization data
  const contactInfo = [
    {
      icon: MapPin,
      title: 'Location',
      details: orgInfo?.location ? [orgInfo.location] : ['Location not available']
    },
    {
      icon: Phone,
      title: 'Phone',
      details: orgInfo?.phone ? [orgInfo.phone] : ['Phone not available']
    },
    {
      icon: Mail,
      title: 'Email',
      details: orgInfo?.email ? [orgInfo.email] : ['Email not available']
    },
    {
      icon: Clock,
      title: 'Hours',
      details: ['Mon - Sat: 9:00 AM - 8:00 PM', 'Sunday: 10:00 AM - 6:00 PM']
    }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await submitContactForm(formData);
      if (response && response.status === "200") {
        // Reset form on success
        setFormData({
          name: '',
          email: '',
          phone: '',
          service: '',
          message: ''
        });
      }
    } catch (error) {
      // Error handled silently
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleWhatsApp = () => {
    const phone = orgInfo?.phone || '9876543210';
    const cleanPhone = phone.replace(/\D/g, ''); // Remove non-digits
    const message = encodeURIComponent(`Hi! I'd like to book an appointment at ${orgInfo?.name || 'Beauty Parlour'}.`);
    window.open(`https://wa.me/${cleanPhone}?text=${message}`, '_blank');
  };

  const handleCall = () => {
    const phone = orgInfo?.phone || '9876543210';
    window.open(`tel:${phone}`, '_self');
  };

  // Function to create a working Google Maps embed URL
  const getMapUrl = () => {
    // If we have organization mapping URL, try to fix it if it's broken
    if (orgInfo?.mapping_url) {
      let mapUrl = orgInfo.mapping_url;

      // Fix common issues with Google Maps URLs
      if (mapUrl.includes('google.com/maps') && !mapUrl.includes('/embed')) {
        // Convert regular Google Maps URL to embed URL
        if (mapUrl.includes('/@')) {
          // Extract coordinates from URL like: /@28.1876096,81.6931286,17z
          const coordMatch = mapUrl.match(/@(-?\d+\.?\d*),(-?\d+\.?\d*)/);
          if (coordMatch) {
            const lat = coordMatch[1];
            const lng = coordMatch[2];
            return `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3516.648298463324!2d${lng}!3d${lat}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjjCsDExJzE1LjQiTiA4McKwNDEnMzUuMyJF!5e0!3m2!1sen!2snp!4v1641234567890!5m2!1sen!2snp`;
          }
        }

        // If it's a place URL, convert to embed
        if (mapUrl.includes('/place/')) {
          const placeMatch = mapUrl.match(/\/place\/([^\/]+)/);
          if (placeMatch) {
            const place = encodeURIComponent(placeMatch[1]);
            return `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3516.648298463324!2d81.69312861488233!3d28.18760960837532!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2z${place}!5e0!3m2!1sen!2snp!4v1641234567890!5m2!1sen!2snp`;
          }
        }
      }

      // If it's already an embed URL but has issues, try to fix the pb parameter
      if (mapUrl.includes('/embed') && mapUrl.includes('pb=')) {
        // Check if pb parameter is incomplete
        if (mapUrl.includes('pb=!1m18!1m12!1m3!1d3516.648298463324!2d81.69312861488233!3d28.18760960837532!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39986510239b6e5d%3A0xc6')) {
          // This is your broken URL - let's fix it with a complete pb parameter
          return 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3516.648298463324!2d81.69312861488233!3d28.18760960837532!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39986510239b6e5d%3A0xc6f0c1b2a3d4e5f6!2sPokhara%2C%20Nepal!5e0!3m2!1sen!2snp!4v1641234567890!5m2!1sen!2snp';
        }

        // Return the URL as-is if it looks valid
        return mapUrl;
      }
    }

    // Fallback: Create a simple embed URL for the location
    if (orgInfo?.location) {
      const location = encodeURIComponent(orgInfo.location);
      // Create a working embed URL for Pokhara, Nepal (default coordinates)
      return `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3516.648298463324!2d81.69312861488233!3d28.18760960837532!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39986510239b6e5d%3A0xc6f0c1b2a3d4e5f6!2s${location}!5e0!3m2!1sen!2snp!4v1641234567890!5m2!1sen!2snp`;
    }

    return null;
  };

  return (
    <div className="pt-20 min-h-screen bg-cream-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold font-playfair text-burgundy-800 mb-4">
            Get In Touch
          </h1>
          <p className="text-xl text-burgundy-600 max-w-2xl mx-auto">
            Ready to experience luxury beauty care? Contact us today to book your appointment
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">

          {/* Contact Form */}
          <Card className="bg-white/90 backdrop-blur-sm border-cream-300 w-full">
            <CardHeader>
              <CardTitle className="text-2xl font-playfair text-burgundy-800">
                Send us a Message
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-burgundy-700 mb-2">
                      Full Name *
                    </label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="border-burgundy-200 focus:border-burgundy-400"
                      placeholder="Your full name"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-burgundy-700 mb-2">
                      Email *
                    </label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="border-burgundy-200 focus:border-burgundy-400"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-burgundy-700 mb-2">
                      Phone Number
                    </label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="border-burgundy-200 focus:border-burgundy-400"
                      placeholder="+977 9848xxxxxx"
                    />
                  </div>
                  <div>
                    <label htmlFor="service" className="block text-sm font-medium text-burgundy-700 mb-2">
                      Service of Interest
                    </label>
                    <Input
                      id="service"
                      name="service"
                      value={formData.service}
                      onChange={handleInputChange}
                      className="border-burgundy-200 focus:border-burgundy-400"
                      placeholder="e.g., Bridal Makeup, Facial"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium  text-burgundy-700 mb-2">
                    Message *
                  </label>
                  <Textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={4}
                    className="border-burgundy-200 focus:border-burgundy-400 h-52"
                    placeholder="Tell us about your requirements, preferred date/time, or any questions you may have..."
                  />
                </div>

                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full luxury-gradient text-white hover:opacity-90 py-3"
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <div className="space-y-8">
            {/* Quick Contact Buttons */}
            <div className="grid grid-cols-2 gap-4">
              <Button
                onClick={handleWhatsApp}
                className="luxury-gradient text-white hover:opacity-90 py-6 text-lg"
              >
                WhatsApp Us
              </Button>
              <Button
                onClick={handleCall}
                variant="outline"
                className="border-2 border-burgundy-800 text-burgundy-800 hover:bg-burgundy-800 hover:text-white py-6 text-lg"
              >
                Call Now
              </Button>
            </div>

            {/* Contact Details */}
            <div className="space-y-6">
              {contactInfo.map((info, index) => (
                <Card key={index} className="bg-white/90 backdrop-blur-sm border-cream-300">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center">
                        <info.icon className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold font-playfair text-burgundy-800">
                          {info.title}
                        </h3>
                        {info.details.map((detail, detailIndex) => (
                          <p key={detailIndex} className="text-burgundy-600">
                            {detail}
                          </p>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Follow Us Section */}
        

        {/* Map Section - Full Width */}
        <div className="mt-12">
          <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
            <CardContent className="p-8">
              <h3 className="text-2xl font-semibold font-playfair text-burgundy-800 mb-6 text-center">
                Find Us
              </h3>
              {(() => {
                const mapUrl = getMapUrl();

                if (mapUrl) {
                  return (
                    <div className="w-full h-96 rounded-lg overflow-hidden border border-cream-300 bg-gray-100">
                      <iframe
                        src={mapUrl}
                        width="100%"
                        height="100%"
                        style={{ border: 0 }}
                        allowFullScreen
                        loading="lazy"
                        referrerPolicy="no-referrer-when-downgrade"
                        title={`Map showing location of ${orgInfo?.name || 'our business'}`}
                        className="rounded-lg"
                        onError={(e) => {
                          // Hide the iframe and show fallback
                          e.currentTarget.style.display = 'none';
                        }}
                      ></iframe>
                    </div>
                  );
                }

                // Fallback when no valid map URL is available
                return (
                  <div className="w-full h-96 bg-cream-300 rounded-lg flex items-center justify-center border border-cream-400">
                    <div className="text-center text-burgundy-600">
                      <MapPin className="w-16 h-16 mx-auto mb-4" />
                      <p className="font-semibold text-lg">Find Us</p>
                      <p className="text-base mt-2">
                        {orgInfo?.location || 'Visit us at our location'}
                      </p>
                      {orgInfo?.location && (
                        <button
                          onClick={() => {
                            const searchUrl = `https://www.google.com/maps/search/${encodeURIComponent(orgInfo.location)}`;
                            window.open(searchUrl, '_blank');
                          }}
                          className="mt-4 px-6 py-3 bg-burgundy-600 text-white rounded-lg text-base hover:bg-burgundy-700 transition-colors"
                        >
                          Open in Google Maps
                        </button>
                      )}
                    </div>
                  </div>
                );
              })()}
            </CardContent>
          </Card>
        </div>
              <div className="mt-12 text-center">
          <Card className="bg-white/90 backdrop-blur-sm border-cream-300 max-w-2xl mx-auto">
            <CardContent className="p-8">
              <h3 className="text-2xl font-semibold font-playfair text-burgundy-800 mb-6">
                Follow Us
              </h3>
              <div className="flex justify-center space-x-6">
                <Button
                  variant="outline"
                  size="lg"
                  className="border-burgundy-300 text-burgundy-700 hover:bg-gold-600 px-8"
                  onClick={() => window.open('https://www.instagram.com/mahbeauty_in', '_blank')}
                >
                  Instagram
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-burgundy-300 text-burgundy-700 hover:bg-gold-600 px-8"
                  onClick={() => window.open('https://www.facebook.com/mahbeauty', '_blank')}
                >
                  Facebook
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-burgundy-300 text-burgundy-700 hover:bg-gold-600 px-8"
                  onClick={() => window.open('https://www.youtube.com/@mahbeauty', '_blank')}
                >
                  YouTube
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
        {/* FAQ Section */}
        <div className="mt-20 cream-gradient rounded-2xl p-12">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold font-playfair text-burgundy-800 mb-4">
              Frequently Asked Questions
            </h2>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold font-playfair text-burgundy-800 mb-2">
                How do I book an appointment?
              </h3>
              <p className="text-burgundy-600 mb-4">
                You can book an appointment by calling us, sending a WhatsApp message, or filling out the contact form above. We'll confirm your slot within 2 hours.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold font-playfair text-burgundy-800 mb-2">
                Do you offer home services?
              </h3>
              <p className="text-burgundy-600 mb-4">
                Yes, we offer premium home services for bridal makeup and special occasions. Additional charges apply based on location and service requirements.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold font-playfair text-burgundy-800 mb-2">
                What products do you use?
              </h3>
              <p className="text-burgundy-600 mb-4">
                We use only premium international brands and certified organic products to ensure the best results and skin safety for all our clients.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold font-playfair text-burgundy-800 mb-2">
                Can I reschedule my appointment?
              </h3>
              <p className="text-burgundy-600 mb-4">
                Yes, you can reschedule your appointment up to 24 hours in advance. Please contact us as soon as possible to avoid any inconvenience.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;