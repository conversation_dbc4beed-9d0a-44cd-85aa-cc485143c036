import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Package, ArrowLeft, Eye, Truck, Calendar } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getUserOrders } from '@/api/apiService';
import { Order } from '@/types/ecommerce';

const Orders = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);

  // Redirect if not authenticated
  if (!isAuthenticated) {
    navigate('/login');
    return null;
  }

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        const response = await getUserOrders();
        if (response.success && response.data) {
          const orderData = Array.isArray(response.data) ? response.data : [];
          setOrders(orderData);
        }
      } catch (error) {
        console.error('Error fetching orders:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'packed':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancel':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="pt-20 min-h-screen bg-cream-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-burgundy-800 mx-auto"></div>
            <p className="mt-4 text-burgundy-600">Loading your orders...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-20 min-h-screen bg-cream-200">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-playfair font-bold text-burgundy-800">
              My Orders
            </h1>
            <p className="text-burgundy-600 mt-2">
              Track and manage your orders
            </p>
          </div>
          <Button
            variant="outline"
            onClick={() => navigate('/profile')}
            className="border-burgundy-300 text-burgundy-700 hover:bg-burgundy-50"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Profile
          </Button>
        </div>

        {orders.length === 0 ? (
          <Card className="bg-white/90 backdrop-blur-sm border-cream-300">
            <CardContent className="text-center py-12">
              <Package className="mx-auto h-16 w-16 text-burgundy-300 mb-4" />
              <h3 className="text-xl font-playfair font-semibold text-burgundy-800 mb-2">
                No orders found
              </h3>
              <p className="text-burgundy-600 mb-6">
                You haven't placed any orders yet. Start shopping to see your orders here.
              </p>
              <Button
                className="luxury-gradient text-white hover:opacity-90"
                onClick={() => navigate('/products')}
              >
                Start Shopping
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {orders.map((order) => (
              <Card key={order.id} className="bg-white/90 backdrop-blur-sm border-cream-300">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center text-burgundy-800">
                      <Package className="w-5 h-5 mr-2" />
                      Order #{order.order_number}
                    </CardTitle>
                    <Badge className={getStatusColor(order.status)}>
                      {order.status}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm text-burgundy-600 flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        Order Date
                      </p>
                      <p className="font-medium text-burgundy-800">
                        {formatDate(order.created_at)}
                      </p>
                    </div>
                    
                    <div>
                      <p className="text-sm text-burgundy-600 flex items-center">
                        <Truck className="w-4 h-4 mr-1" />
                        Delivery Address
                      </p>
                      <p className="font-medium text-burgundy-800">
                        {order.area}, {order.state}
                      </p>
                    </div>
                    
                    <div>
                      <p className="text-sm text-burgundy-600">Total Amount</p>
                      <p className="text-xl font-bold text-burgundy-800">
                        Rs. {order.grand_total.toFixed(2)}
                      </p>
                    </div>
                  </div>
                  
                  {order.order_note && (
                    <div>
                      <p className="text-sm text-burgundy-600">Order Notes</p>
                      <p className="text-burgundy-800">{order.order_note}</p>
                    </div>
                  )}
                  
                  <Separator className="bg-burgundy-200" />
                  
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-burgundy-600">
                      <p>Delivery to: {order.first_name} {order.last_name}</p>
                      <p>{order.phone} • {order.email}</p>
                    </div>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-burgundy-300 text-burgundy-700 hover:bg-burgundy-50"
                      onClick={() => navigate(`/orders/${order.id}`)}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Orders;
