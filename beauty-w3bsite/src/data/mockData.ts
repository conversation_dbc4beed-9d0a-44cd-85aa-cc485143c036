// Centralized mock data for all entities
// This file serves as a single source of truth for all mock data
// and provides interfaces that match the expected API response structure

export interface Product {
  id: number;
  name: string;
  description: string;
  price: string;
  image: string;
  image_url?: string;
  category: string;
  category_display?: string;
  rating: number;
  in_stock?: boolean;
  inStock?: boolean; // Keep for backward compatibility
  ingredients?: Array<{
    name: string;
    quantity: string;
  }> | string[]; // Support both formats for backward compatibility
  usage_instructions?: string;
  usage?: string; // Keep for backward compatibility
  benefits?: string[];
  created_at?: string;
  updated_at?: string;
  is_active?: boolean;
}

export interface Service {
  id: number;
  name: string;
  description: string;
  duration: string;
  price: string;
  image?: string;
  image_url?: string;
  category: number | string; // Can be ID or name for backward compatibility
  category_name?: string;
  popular: boolean;
  process_steps?: string[];
  process?: string[]; // Keep for backward compatibility
  benefits?: string[];
  equipment_used?: string[];
  equipment?: string[]; // Keep for backward compatibility
  staff_qualification?: string;
  staff?: string; // Keep for backward compatibility
  faq?: { question: string; answer: string }[];
  before_after_images?: Array<{
    id: number;
    before_image: string;
    before_image_url: string;
    after_image: string;
    after_image_url: string;
  }>; // New backend format
  beforeAfterImages?: { before: string; after: string }[]; // Keep for backward compatibility
  created_at?: string;
  updated_at?: string;
  is_active?: boolean;
}

export interface ServiceCategory {
  title: string;
  services: Service[];
}

export interface Testimonial {
  id: number;
  name: string;
  rating: number;
  comment: string;
  service: string;
  image: string;
  date?: string;
}

export interface ContactInfo {
  icon: any;
  title: string;
  details: string[];
}

export interface GalleryImage {
  id: number;
  url: string;
  category: string;
  alt: string;
  before?: string;
  after?: string;
}

// Mock Products Data
export const mockProducts: Product[] = [
  {
    id: 1,
    name: 'Luxury Facial Serum',
    description: 'Anti-aging serum with premium ingredients for youthful, glowing skin',
    price: '₹3,500',
    image: 'https://images.unsplash.com/photo-1611930022073-b7a4ba5fcccd?q=80&w=2067&auto=format&fit=crop',
    category: 'Skincare',
    rating: 4.8,
    inStock: true,
    ingredients: ['Hyaluronic Acid', 'Vitamin C', 'Retinol', 'Peptides'],
    usage: 'Apply 2-3 drops to clean face twice daily. Follow with moisturizer.',
    benefits: ['Reduces fine lines', 'Improves skin texture', 'Boosts hydration', 'Evens skin tone']
  },
  {
    id: 2,
    name: '24K Gold Face Mask',
    description: 'Luxurious gold-infused face mask for radiant and rejuvenated skin',
    price: '₹2,800',
    image: 'https://images.unsplash.com/photo-1596755389378-c31d21fd1273?q=80&w=2088&auto=format&fit=crop',
    category: 'Skincare',
    rating: 4.9,
    inStock: true,
    ingredients: ['24K Gold Particles', 'Collagen', 'Vitamin E', 'Aloe Vera'],
    usage: 'Apply evenly to clean face. Leave for 15-20 minutes, then rinse with warm water.',
    benefits: ['Anti-aging properties', 'Deep moisturization', 'Improves elasticity', 'Gives golden glow']
  },
  {
    id: 3,
    name: 'Diamond Glow Moisturizer',
    description: 'Premium moisturizer with diamond particles for ultimate skin radiance',
    price: '₹4,200',
    image: 'https://images.unsplash.com/photo-1620916566398-39f1143ab7be?q=80&w=2087&auto=format&fit=crop',
    category: 'Skincare',
    rating: 4.7,
    inStock: false,
    ingredients: ['Diamond Powder', 'Shea Butter', 'Jojoba Oil', 'Ceramides'],
    usage: 'Apply to face and neck morning and evening after cleansing.',
    benefits: ['Intense hydration', 'Skin brightening', 'Reduces dullness', 'Long-lasting moisture']
  }
];

// Mock Services Data
export const mockServiceCategories: ServiceCategory[] = [
  {
    title: 'Facial Treatments',
    services: [
      {
        id: 1,
        name: 'Diamond Facial',
        description: 'Luxurious diamond-infused facial for radiant, glowing skin',
        duration: '90 minutes',
        price: '₹4,500',
        image: 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?q=80&w=2070&auto=format&fit=crop',
        category: 'Facial Treatments',
        popular: true,
        process: [
          'Deep cleansing with premium products',
          'Gentle exfoliation to remove dead skin',
          'Diamond microdermabrasion treatment',
          'Nourishing face mask application',
          'Moisturizing and sun protection'
        ],
        benefits: [
          'Removes dead skin cells',
          'Improves skin texture and tone',
          'Reduces fine lines and wrinkles',
          'Enhances skin radiance',
          'Stimulates collagen production'
        ],
        equipment: ['Diamond microdermabrasion machine', 'Steamer', 'High-frequency device'],
        staff: 'Certified aesthetician with 5+ years experience',
        faq: [
          {
            question: 'Is the diamond facial suitable for sensitive skin?',
            answer: 'Yes, we customize the treatment intensity based on your skin type and sensitivity.'
          },
          {
            question: 'How often should I get a diamond facial?',
            answer: 'For best results, we recommend monthly treatments.'
          }
        ],
        beforeAfterImages: [
          {
            before: 'https://images.unsplash.com/photo-1515377905703-c4788e51af15?q=80&w=2070&auto=format&fit=crop',
            after: 'https://images.unsplash.com/photo-1594824719871-8dd3c0651d32?q=80&w=2070&auto=format&fit=crop'
          }
        ]
      },
      {
        id: 2,
        name: 'Gold Facial',
        description: '24k gold facial treatment for anti-aging and skin rejuvenation',
        duration: '75 minutes',
        price: '₹3,800',
        image: 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?q=80&w=2070&auto=format&fit=crop',
        category: 'Facial Treatments',
        popular: false,
        process: [
          'Thorough skin analysis and cleansing',
          'Steam treatment to open pores',
          '24K gold mask application',
          'Gentle massage with gold-infused serum',
          'Final moisturizing treatment'
        ],
        benefits: [
          'Anti-aging properties',
          'Improves blood circulation',
          'Reduces inflammation',
          'Enhances skin elasticity',
          'Provides natural glow'
        ],
        equipment: ['Gold ion machine', 'Ultrasonic device', 'LED therapy panel'],
        staff: 'Gold treatment specialist with international certification'
      }
    ]
  },
  {
    title: 'Hair Services',
    services: [
      {
        id: 3,
        name: 'Bridal Hair Styling',
        description: 'Complete bridal hair makeover with traditional and modern styles',
        duration: '3 hours',
        price: '₹5,500',
        image: 'https://images.unsplash.com/photo-1560066984-138dadb4c035?q=80&w=2074&auto=format&fit=crop',
        category: 'Hair Services',
        popular: true,
        process: [
          'Hair consultation and style selection',
          'Hair washing and conditioning treatment',
          'Hair styling with professional tools',
          'Hair accessories and ornament placement',
          'Final touch-ups and setting spray'
        ],
        benefits: [
          'Long-lasting hairstyle',
          'Professional finish',
          'Customized to face shape',
          'Photo-ready appearance',
          'Stress-free bridal experience'
        ],
        equipment: ['Professional curling irons', 'Hair straighteners', 'Blow dryers', 'Hair accessories'],
        staff: 'Bridal hair specialist with 8+ years experience'
      }
    ]
  },
  {
    title: 'Makeup Services',
    services: [
      {
        id: 4,
        name: 'Bridal Makeup',
        description: 'Complete bridal makeup package with trial session included',
        duration: '4 hours',
        price: '₹8,500',
        image: 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?q=80&w=2069&auto=format&fit=crop',
        category: 'Makeup Services',
        popular: true,
        process: [
          'Pre-bridal skin preparation',
          'Base makeup application',
          'Eye makeup with false lashes',
          'Contouring and highlighting',
          'Final setting and touch-ups'
        ],
        benefits: [
          'Long-lasting makeup',
          'Professional photography ready',
          'Customized to skin tone',
          'Includes trial session',
          'Premium product usage'
        ],
        equipment: ['Professional makeup brushes', 'Airbrush system', 'Ring light', 'Makeup palette'],
        staff: 'Certified bridal makeup artist'
      }
    ]
  }
];

// Mock Testimonials Data
export const mockTestimonials: Testimonial[] = [
  {
    id: 1,
    name: 'Priya Sharma',
    rating: 5,
    comment: 'Absolutely stunning results! The team is incredibly professional and the ambiance is so relaxing. I felt like a queen during my bridal preparation.',
    service: 'Bridal Package',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b332c2de?q=80&w=2187&auto=format&fit=crop',
    date: '2024-01-15'
  },
  {
    id: 2,
    name: 'Anita Gupta',
    rating: 5,
    comment: 'Best facial treatment I\'ve ever had. My skin has never looked better! The diamond facial was truly transformative and worth every penny.',
    service: 'Diamond Facial',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=2070&auto=format&fit=crop',
    date: '2024-01-20'
  },
  {
    id: 3,
    name: 'Meera Joshi',
    rating: 5,
    comment: 'The hair styling was perfect for my wedding. Highly recommend! The team understood exactly what I wanted and executed it flawlessly.',
    service: 'Bridal Hair',
    image: 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?q=80&w=2069&auto=format&fit=crop',
    date: '2024-02-01'
  },
  {
    id: 4,
    name: 'Ritu Malhotra',
    rating: 5,
    comment: 'Amazing experience from start to finish. The gold facial left my skin glowing for weeks. The luxury and attention to detail is unmatched.',
    service: 'Gold Facial',
    image: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?q=80&w=2176&auto=format&fit=crop',
    date: '2024-02-10'
  },
  {
    id: 5,
    name: 'Kavya Reddy',
    rating: 5,
    comment: 'Professional service with outstanding results. My party makeup was flawless and lasted the entire event. Will definitely be returning!',
    service: 'Party Makeup',
    image: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?q=80&w=2070&auto=format&fit=crop',
    date: '2024-02-15'
  },
  {
    id: 6,
    name: 'Sunita Patel',
    rating: 5,
    comment: 'The keratin treatment has transformed my hair completely. No more frizz and such beautiful shine. The staff is knowledgeable and caring.',
    service: 'Keratin Treatment',
    image: 'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?q=80&w=2187&auto=format&fit=crop',
    date: '2024-02-20'
  }
];

// Mock Gallery Images Data
export const mockGalleryImages: GalleryImage[] = [
  {
    id: 1,
    url: 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?q=80&w=2069&auto=format&fit=crop',
    category: 'makeup',
    alt: 'Bridal makeup transformation'
  },
  {
    id: 2,
    url: 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?q=80&w=2070&auto=format&fit=crop',
    category: 'facial',
    alt: 'Diamond facial treatment'
  },
  {
    id: 3,
    url: 'https://images.unsplash.com/photo-1560066984-138dadb4c035?q=80&w=2074&auto=format&fit=crop',
    category: 'hair',
    alt: 'Bridal hair styling'
  }
];